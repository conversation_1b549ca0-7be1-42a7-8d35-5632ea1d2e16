# **CHIA - Flujos de Usuario Detallados**
## *Diagramas de Flujo con Casos Límite y Manejo de Errores*

---

## **1. <PERSON><PERSON><PERSON> Principal: Consulta con IA**

### **Diagrama de Flujo**

```mermaid
flowchart TD
    A[Usuario accede a CHIA] --> B{¿Usuario autenticado?}
    B -->|No| C[Mostrar opciones de consulta pública]
    B -->|Sí| D[Mostrar dashboard personalizado]
    
    C --> E[Iniciar chat con IA]
    D --> E
    
    E --> F[Usuario escribe consulta]
    F --> G[IA procesa consulta]
    G --> H{¿Consulta válida?}
    
    H -->|No| I[Mostrar mensaje de error<br/>Sugerir reformular]
    I --> F
    
    H -->|Sí| J{¿Requiere autenticación?}
    J -->|Sí| K{¿Usuario autenticado?}
    K -->|No| L[Solicitar autenticación]
    L --> M[Proceso de login]
    M --> N{¿Login exitoso?}
    N -->|No| O[Mostrar error de autenticación]
    O --> L
    N -->|Sí| P[Continuar con consulta]
    
    J -->|No| P
    K -->|Sí| P
    
    P --> Q[IA genera respuesta]
    Q --> R{¿Respuesta completa?}
    R -->|No| S[Mostrar respuesta parcial<br/>Ofrecer más información]
    R -->|Sí| T[Mostrar respuesta completa]
    
    S --> U[¿Usuario solicita más info?]
    U -->|Sí| V[IA amplía respuesta]
    U -->|No| W[Finalizar consulta]
    
    T --> X{¿Requiere trámite?}
    X -->|No| W
    X -->|Sí| Y[Ofrecer iniciar trámite]
    Y --> Z{¿Usuario acepta?}
    Z -->|No| W
    Z -->|Sí| AA[Redirigir a formulario de trámite]
    
    V --> T
    W --> BB[Guardar historial]
    AA --> BB
    BB --> CC[Fin del flujo]
```

### **Casos Límite y Errores**

#### **Error de Conectividad**
- **Escenario**: Usuario pierde conexión durante la consulta
- **Manejo**: 
  - Mostrar mensaje "Sin conexión"
  - Guardar consulta en localStorage
  - Reenviar automáticamente al recuperar conexión
  - Timeout de 30 segundos para reintentos

#### **IA No Disponible**
- **Escenario**: Servicio de IA está caído
- **Manejo**:
  - Mostrar mensaje de mantenimiento
  - Ofrecer consulta por formulario tradicional
  - Notificar tiempo estimado de recuperación
  - Permitir dejar contacto para seguimiento

#### **Consulta Ambigua**
- **Escenario**: IA no puede interpretar la consulta
- **Manejo**:
  - Solicitar aclaración específica
  - Ofrecer ejemplos de consultas válidas
  - Mostrar categorías de trámites disponibles
  - Permitir reformular hasta 3 veces

---

## **2. Flujo de Autenticación**

### **Diagrama de Flujo**

```mermaid
flowchart TD
    A[Usuario hace click en 'Ingresar'] --> B[Mostrar opciones de autenticación]
    B --> C{Método seleccionado}
    
    C -->|Cédula + Contraseña| D[Formulario login tradicional]
    C -->|Biometría| E[Solicitar huella/facial]
    C -->|Cédula Digital| F[Integración con app oficial]
    
    D --> G[Validar credenciales]
    E --> H[Validar biometría]
    F --> I[Validar con sistema externo]
    
    G --> J{¿Credenciales válidas?}
    H --> K{¿Biometría válida?}
    I --> L{¿Validación externa OK?}
    
    J -->|No| M[Incrementar contador fallos]
    K -->|No| M
    L -->|No| M
    
    M --> N{¿Fallos < 3?}
    N -->|Sí| O[Mostrar error, permitir reintento]
    N -->|No| P[Bloquear cuenta temporalmente]
    
    O --> Q{¿Método alternativo?}
    Q -->|Sí| R[Ofrecer recuperación]
    Q -->|No| D
    
    P --> S[Mostrar mensaje de bloqueo]
    S --> T[Ofrecer recuperación por email/SMS]
    
    J -->|Sí| U[Crear sesión]
    K -->|Sí| U
    L -->|Sí| U
    
    U --> V[Verificar permisos usuario]
    V --> W{¿Permisos válidos?}
    W -->|No| X[Mostrar acceso limitado]
    W -->|Sí| Y[Acceso completo]
    
    X --> Z[Dashboard básico]
    Y --> AA[Dashboard completo]
    
    R --> BB[Proceso recuperación]
    T --> BB
    BB --> CC[Fin del flujo]
    Z --> CC
    AA --> CC
```

### **Casos Límite y Errores**

#### **Cuenta Bloqueada**
- **Escenario**: Usuario supera intentos de login
- **Manejo**:
  - Bloqueo temporal de 15 minutos
  - Envío de notificación por email/SMS
  - Opción de desbloqueo por verificación adicional
  - Log de seguridad para auditoría

#### **Biometría No Disponible**
- **Escenario**: Dispositivo no soporta biometría
- **Manejo**:
  - Detectar capacidades del dispositivo
  - Ocultar opción si no está disponible
  - Fallback a métodos tradicionales
  - Mensaje explicativo claro

#### **Sesión Expirada**
- **Escenario**: Usuario inactivo por tiempo prolongado
- **Manejo**:
  - Advertencia 5 minutos antes del vencimiento
  - Opción de extender sesión
  - Logout automático con mensaje explicativo
  - Preservar trabajo no guardado en localStorage

---

## **3. Flujo de Trámite: Renovación de Cédula**

### **Diagrama de Flujo**

```mermaid
flowchart TD
    A[Usuario selecciona 'Renovar Cédula'] --> B{¿Usuario autenticado?}
    B -->|No| C[Redirigir a login]
    B -->|Sí| D[Verificar elegibilidad]
    
    C --> E[Proceso de autenticación]
    E --> D
    
    D --> F{¿Elegible para renovación?}
    F -->|No| G[Mostrar razones de inelegibilidad]
    F -->|Sí| H[Mostrar formulario paso 1]
    
    G --> I[Ofrecer alternativas]
    I --> J[Fin del flujo]
    
    H --> K[Usuario completa datos personales]
    K --> L[Validar información en tiempo real]
    L --> M{¿Datos válidos?}
    
    M -->|No| N[Mostrar errores específicos]
    N --> K
    M -->|Sí| O[Avanzar a paso 2]
    
    O --> P[Solicitar documentos]
    P --> Q[Usuario sube archivos]
    Q --> R[Validar formato y tamaño]
    R --> S{¿Documentos válidos?}
    
    S -->|No| T[Mostrar errores de documentos]
    T --> Q
    S -->|Sí| U[Procesar con OCR]
    
    U --> V{¿OCR exitoso?}
    V -->|No| W[Solicitar resubir documentos]
    W --> Q
    V -->|Sí| X[Verificar datos con Registraduría]
    
    X --> Y{¿Verificación exitosa?}
    Y -->|No| Z[Mostrar discrepancias]
    Z --> AA[Permitir corrección manual]
    AA --> BB[Revisión manual requerida]
    
    Y -->|Sí| CC[Mostrar resumen]
    CC --> DD[Usuario confirma información]
    DD --> EE{¿Usuario confirma?}
    
    EE -->|No| FF[Permitir edición]
    FF --> K
    EE -->|Sí| GG[Procesar pago]
    
    GG --> HH{¿Pago exitoso?}
    HH -->|No| II[Mostrar error de pago]
    II --> JJ[Ofrecer métodos alternativos]
    JJ --> GG
    
    HH -->|Sí| KK[Generar número de radicado]
    KK --> LL[Enviar a cola de procesamiento]
    LL --> MM[Mostrar confirmación]
    MM --> NN[Enviar notificaciones]
    NN --> OO[Fin del flujo]
    
    BB --> PP[Notificar usuario sobre revisión]
    PP --> OO
```

### **Casos Límite y Errores**

#### **Documentos Ilegibles**
- **Escenario**: OCR no puede procesar documentos
- **Manejo**:
  - Mostrar guía de fotografía de documentos
  - Permitir hasta 3 reintentos
  - Opción de carga manual de datos
  - Escalación a revisión humana

#### **Discrepancia de Datos**
- **Escenario**: Datos no coinciden con registros oficiales
- **Manejo**:
  - Mostrar diferencias específicas
  - Permitir justificación del usuario
  - Solicitar documentos adicionales
  - Crear caso para revisión manual

#### **Fallo en Pago**
- **Escenario**: Error en procesamiento de pago
- **Manejo**:
  - Preservar información del trámite
  - Ofrecer múltiples métodos de pago
  - Permitir pago posterior con código de reserva
  - Timeout de 24 horas para completar pago

---

## **4. Flujo de Recuperación de Errores**

### **Estrategias Generales**

#### **Reconexión Automática**
```mermaid
flowchart TD
    A[Error de conexión detectado] --> B[Mostrar indicador offline]
    B --> C[Intentar reconexión cada 5s]
    C --> D{¿Conexión restaurada?}
    D -->|No| E{¿Intentos < 10?}
    E -->|Sí| C
    E -->|No| F[Mostrar modo offline]
    D -->|Sí| G[Sincronizar datos pendientes]
    G --> H[Restaurar funcionalidad completa]
```

#### **Recuperación de Sesión**
```mermaid
flowchart TD
    A[Página recargada/cerrada] --> B[Verificar localStorage]
    B --> C{¿Datos de sesión?}
    C -->|No| D[Inicio normal]
    C -->|Sí| E[Validar token de sesión]
    E --> F{¿Token válido?}
    F -->|No| G[Limpiar datos, login requerido]
    F -->|Sí| H[Restaurar estado de aplicación]
    H --> I[Recuperar trabajo no guardado]
```

#### **Manejo de Estados Inconsistentes**
```mermaid
flowchart TD
    A[Estado inconsistente detectado] --> B[Registrar error en logs]
    B --> C[Intentar corrección automática]
    C --> D{¿Corrección exitosa?}
    D -->|Sí| E[Continuar operación normal]
    D -->|No| F[Mostrar opción de reinicio]
    F --> G[Usuario acepta reinicio]
    G --> H[Limpiar estado local]
    H --> I[Recargar desde servidor]
```

---

## **5. Especificaciones de Interacción**

### **Tiempos de Respuesta**
- **Consulta IA**: Máximo 3 segundos
- **Validación de formularios**: Tiempo real (< 500ms)
- **Carga de documentos**: Progreso visible, máximo 30 segundos
- **Procesamiento de pagos**: Máximo 10 segundos

### **Indicadores de Estado**
- **Loading spinners**: Para operaciones < 3 segundos
- **Progress bars**: Para operaciones > 3 segundos
- **Skeleton screens**: Para carga inicial de contenido
- **Toast notifications**: Para confirmaciones y errores

### **Navegación y Breadcrumbs**
- **Breadcrumbs**: Visibles en todos los formularios multi-paso
- **Navegación lateral**: Persistente en procesos largos
- **Botones de acción**: Siempre visibles (sticky)
- **Confirmaciones**: Para acciones destructivas

---

*Estos flujos garantizan una experiencia robusta y resiliente, manejando todos los casos límite y errores posibles en el sistema CHIA.*
