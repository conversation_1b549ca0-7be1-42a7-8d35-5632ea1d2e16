# Chia-Next AI-First Citizen Services Platform - Fullstack Architecture Document

This document outlines the complete fullstack architecture for Chia-Next AI-First Citizen Services Platform, including backend systems, frontend implementation, and their integration. It serves as the single source of truth for AI-driven development, ensuring consistency across the entire technology stack.

This unified approach combines what would traditionally be separate backend and frontend architecture documents, streamlining the development process for modern fullstack applications where these concerns are increasingly intertwined.

### Starter Template or Existing Project

**Status:** Greenfield project with comprehensive documentation foundation

The project builds upon extensive strategic documentation (PRD_chia.md, ARQUITECTURA.md) but requires complete technical implementation. No existing codebase or starter templates are currently in use, providing full architectural flexibility while adhering to the documented vision of an AI-first government services platform.

### Change Log

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |
| 2024-12-19 | 1.0 | Initial fullstack architecture document | Winston - Holistic System Architect |

## High Level Architecture

### Technical Summary

The Chia-Next platform employs a modern Jamstack architecture with serverless backend services, optimized for AI-first citizen interactions and government service delivery. The frontend leverages Next.js 15 with App Router for optimal SEO and performance, while Supabase provides a comprehensive backend-as-a-service solution with PostgreSQL, real-time capabilities, and built-in authentication. The architecture centers around OpenAI GPT-4 integration for conversational AI, semantic search via pgvector embeddings, and progressive enhancement from consultation services (Phase 1) to automated transaction processing (Phase 2). This design achieves the PRD goals of 70% reduction in consultation response times, 85% citizen satisfaction, and 60% automation of frequent procedures while maintaining government-grade security and scalability.

### Platform and Infrastructure Choice

**Platform Options Analysis:**

1. **Vercel + Supabase (Recommended)**
   - **Pros:** Rapid development, built-in Next.js optimization, automatic scaling, integrated auth/storage, excellent developer experience
   - **Cons:** Vendor lock-in, limited backend customization
   - **Best for:** AI-first applications requiring rapid iteration and proven scalability

2. **AWS Full Stack**
   - **Pros:** Enterprise scale, complete control, extensive service ecosystem
   - **Cons:** Complex setup, higher operational overhead, longer development cycles
   - **Best for:** Large-scale enterprise deployments with complex compliance requirements

3. **Google Cloud + Firebase**
   - **Pros:** Strong AI/ML integration, real-time capabilities, good mobile support
   - **Cons:** Less mature Next.js integration, limited PostgreSQL options
   - **Best for:** Mobile-first applications with heavy ML requirements

**Recommendation:** Vercel + Supabase provides the optimal balance of development velocity, AI integration capabilities, and government-appropriate security features while maintaining cost predictability and operational simplicity.

**Platform:** Vercel + Supabase
**Key Services:** Vercel (hosting, edge functions), Supabase (PostgreSQL, Auth, Realtime, Storage), OpenAI (GPT-4, Embeddings)
**Deployment Host and Regions:** Vercel Edge Network (global), Supabase (US-East-1 primary, multi-region capability)

### Repository Structure

**Structure:** Monorepo with workspace-based organization
**Monorepo Tool:** npm workspaces (built-in, simple, Next.js compatible)
**Package Organization:** Feature-based packages with shared utilities, separate apps for citizen portal and admin panel

### High Level Architecture Diagram

```mermaid
graph TB
    subgraph "User Layer"
        C[Citizens] 
        A[Administrators]
        M[Mobile Users]
    end
    
    subgraph "Edge Layer - Vercel"
        CDN[Edge Network/CDN]
        EF[Edge Functions]
    end
    
    subgraph "Application Layer"
        WEB[Next.js Web App]
        ADMIN[Admin Panel]
        API[API Routes]
    end
    
    subgraph "AI Services"
        GPT[OpenAI GPT-4]
        EMB[Embeddings API]
        VS[Vector Search]
    end
    
    subgraph "Backend Services - Supabase"
        AUTH[Supabase Auth]
        DB[(PostgreSQL + pgvector)]
        RT[Realtime]
        STORAGE[File Storage]
        RLS[Row Level Security]
    end
    
    subgraph "External Integrations"
        GOV[Government APIs]
        NOTIFY[Notification Services]
        DOCS[Document Validation]
    end
    
    C --> CDN
    A --> CDN
    M --> CDN
    
    CDN --> WEB
    CDN --> ADMIN
    CDN --> EF
    
    WEB --> API
    ADMIN --> API
    EF --> API
    
    API --> AUTH
    API --> DB
    API --> RT
    API --> STORAGE
    API --> GPT
    API --> EMB
    
    VS --> DB
    GPT --> EMB
    
    API --> GOV
    API --> NOTIFY
    API --> DOCS
    
    DB --> RLS
```

### Architectural Patterns

- **Jamstack Architecture:** Static generation with serverless APIs and edge delivery - _Rationale:_ Optimal performance for government services requiring fast, reliable access with global reach

- **AI-First Design:** Conversational interfaces as primary interaction method - _Rationale:_ Reduces citizen friction and enables natural language interaction with government services

- **Progressive Enhancement:** Core functionality works without JavaScript, enhanced with AI features - _Rationale:_ Ensures accessibility and reliability for all citizens regardless of device capabilities

- **Row Level Security (RLS):** Database-level security policies for data isolation - _Rationale:_ Government-grade security ensuring citizens only access their own data

- **Event-Driven Architecture:** Real-time updates and notifications via Supabase Realtime - _Rationale:_ Keeps citizens informed of process status changes without manual checking

- **Repository Pattern:** Abstracted data access with TypeScript interfaces - _Rationale:_ Enables testing, maintains consistency, and allows future database migrations

- **Component-Based UI:** Reusable React components with TypeScript - _Rationale:_ Maintainability and consistency across citizen and admin interfaces

## Tech Stack

### Technology Stack Table

| Category                 | Technology        | Version     | Purpose     | Rationale      |
| :----------------------- | :---------------- | :---------- | :---------- | :------------- |
| **Frontend Language**    | TypeScript        | 5.3+        | Type-safe frontend development | Government applications require reliability; strong typing prevents runtime errors |
| **Frontend Framework**   | Next.js           | 15.0+       | React framework with App Router | SSR/SSG for SEO, optimal performance, Vercel integration, mature ecosystem |
| **UI Component Library** | Tailwind CSS + Headless UI | 3.4+ / 1.7+ | Styling and accessible components | Rapid development, accessibility compliance, government design system compatibility |
| **State Management**     | Zustand + React Query | 4.4+ / 5.0+ | Client state and server state | Lightweight, TypeScript-first, excellent caching for AI responses |
| **Backend Language**     | TypeScript        | 5.3+        | Type-safe backend development | Code sharing with frontend, consistent development experience |
| **Backend Framework**    | Next.js API Routes + Supabase | 15.0+ / 2.39+ | Serverless API endpoints | Unified deployment, automatic scaling, integrated with frontend |
| **API Style**            | REST + Supabase Client | - | RESTful APIs with real-time capabilities | Standard government API patterns, real-time updates for status tracking |
| **Database**             | PostgreSQL + pgvector | 16+ / 0.5+ | Primary data store with vector search | ACID compliance, JSON support, native vector search for AI embeddings |
| **Cache**                | Vercel Edge Cache + React Query | - | Multi-layer caching strategy | Edge caching for static content, client caching for dynamic data |
| **File Storage**         | Supabase Storage  | 2.39+       | Document and media storage | Integrated with auth, CDN delivery, government document handling |
| **Authentication**       | Supabase Auth     | 2.39+       | User authentication and authorization | Built-in auth flows, social providers, government ID integration ready |
| **AI/ML Services**       | OpenAI GPT-4 + Embeddings | 4.0+ | Conversational AI and semantic search | Industry-leading AI capabilities, stable API, cost-effective scaling |
| **Frontend Testing**     | Jest + React Testing Library | 29+ / 14+ | Unit and integration testing | Standard React testing, accessibility testing built-in |
| **Backend Testing**      | Jest + Supertest  | 29+ / 6.3+  | API testing | Consistent testing framework, easy API endpoint testing |
| **E2E Testing**          | Playwright        | 1.40+       | End-to-end testing | Cross-browser testing, government accessibility compliance testing |
| **Code Quality**         | Ultracite         | Latest      | Linting and code standards | Zero-config linting, Next.js native rules, accessibility compliance, security best practices |
| **Build Tool**           | Next.js built-in  | 15.0+       | Build and bundling | Zero-config, optimized for production, Vercel deployment |
| **Bundler**              | Turbopack         | Built-in    | Fast development builds | Next.js 15 default, significantly faster than Webpack |
| **IaC Tool**             | Vercel CLI + Supabase CLI | Latest | Infrastructure as code | Declarative deployments, environment management |
| **CI/CD**                | GitHub Actions + Vercel | - | Continuous integration and deployment | Integrated with Vercel, automated testing and deployment |
| **Monitoring**           | Vercel Analytics + Supabase Metrics | - | Performance and usage monitoring | Built-in monitoring, government compliance reporting |
| **Logging**              | Vercel Functions Logs + Supabase Logs | - | Application logging and debugging | Centralized logging, error tracking, audit trails |
| **CSS Framework**        | Tailwind CSS      | 3.4+        | Utility-first CSS framework | Rapid development, consistent design system, small bundle size |

## Data Models

### Ciudadano (Citizen)

**Purpose:** Central entity representing citizens interacting with government services, storing personal information, preferences, and interaction history for personalized AI experiences.

**Key Attributes:**

- id: UUID - Unique identifier
- auth_id: UUID - Reference to Supabase auth user
- nombre_completo: string - Full legal name
- identificacion_nacional: string - National ID (unique)
- email: string - Contact email
- telefono: string - Phone number
- preferencias_comunicacion: JSON - Communication preferences
- nivel_asistencia: enum - Assistance level (basico|intermedio|avanzado)
- perfil_usuario: enum - User profile (nuevo|recurrente|power_user|necesita_ayuda)

**TypeScript Interface:**

```typescript
interface Ciudadano {
  id: string;
  auth_id: string;
  nombre_completo?: string;
  identificacion_nacional?: string;
  email?: string;
  telefono?: string;
  preferencias_comunicacion: {
    email: boolean;
    sms: boolean;
    whatsapp?: boolean;
  };
  idioma_preferido: string;
  nivel_asistencia: 'basico' | 'intermedio' | 'avanzado';
  primera_visita: Date;
  ultima_actividad: Date;
  sesiones_completadas: number;
  satisfaccion_promedio?: number;
  perfil_usuario: 'nuevo' | 'recurrente' | 'power_user' | 'necesita_ayuda';
  created_at: Date;
  updated_at: Date;
}
```

**Relationships:**

- Has many ConversacionesIA (1:n)
- Has many SeguimientoTramites (1:n)

### Dependencia (Government Department)

**Purpose:** Represents government departments and agencies, organizing services and enabling proper routing of citizen requests to appropriate authorities.

**Key Attributes:**

- id: UUID - Unique identifier
- nombre: string - Department name
- descripcion: text - Department description
- nivel_jerarquico: number - Hierarchical level for navigation
- contacto_ciudadano: JSON - Citizen-facing contact information
- horarios_atencion: JSON - Service hours
- servicios_destacados: string[] - Featured services

**TypeScript Interface:**

```typescript
interface Dependencia {
  id: string;
  codigo_dependencia: string; // e.g., "000", "010", "030"
  nombre: string;
  sigla?: string;
  descripcion?: string;
  nivel_jerarquico?: number;
  contacto_ciudadano: {
    telefono?: string;
    email?: string;
    direccion?: string;
    sitio_web?: string;
  };
  horarios_atencion: {
    lunes_viernes?: string;
    sabados?: string;
    domingos?: string;
    feriados?: string;
  };
  servicios_destacados: string[];
  activo: boolean;
  metadata_seo?: Record<string, any>;
  created_at: Date;
  updated_at: Date;
}

interface Subdependencia {
  id: string;
  dependencia_id: string;
  codigo_subdependencia: string; // e.g., "001", "011", "031"
  nombre: string;
  sigla?: string;
  descripcion?: string;
  contacto_ciudadano: {
    telefono?: string;
    email?: string;
    direccion?: string;
  };
  horarios_atencion: {
    lunes_viernes?: string;
    sabados?: string;
    domingos?: string;
    feriados?: string;
  };
  activo: boolean;
  created_at: Date;
  updated_at: Date;
}

interface OPA {
  id: string;
  dependencia_id: string;
  subdependencia_id: string;
  codigo_opa: string;
  descripcion: string;
  activa: boolean;
  created_at: Date;
  updated_at: Date;
}

interface FAQTema {
  id: string;
  dependencia_id: string;
  subdependencia_id?: string;
  tema: string;
  descripcion?: string;
  activo: boolean;
  created_at: Date;
  updated_at: Date;
}
```

**Relationships:**

- Has many ServiciosCiudadanos (1:n)
- Has many Subdependencias (1:n)

### ServicioCiudadano (Citizen Service)

**Purpose:** Represents individual government services, procedures, and transactions available to citizens, with AI-optimized content for semantic search and automated assistance.

**Key Attributes:**

- id: UUID - Unique identifier
- dependencia_id: UUID - Reference to owning department
- nombre: string - Service name
- descripcion_completa: text - Detailed description
- tipo_servicio: enum - Service type (tramite|consulta|certificado|permiso)
- palabras_clave: string[] - Keywords for search
- tiempo_estimado_minutos: number - Estimated completion time
- contenido_embedding: vector - AI embeddings for semantic search

**TypeScript Interface:**

```typescript
type TipoServicio = 'tramite' | 'consulta' | 'certificado' | 'permiso' | 'impuesto' | 'licencia';

interface ServicioCiudadano {
  id: string;
  dependencia_id: string;
  subdependencia_id?: string;

  // Service Information (matching tramites JSON structure)
  nombre: string; // "Nombre" field from JSON
  descripcion_corta?: string;
  descripcion_completa?: string;
  tipo_servicio: TipoServicio;

  // Tramite-specific fields from JSON
  codigo_dependencia: string;
  codigo_subdependencia?: string;
  formulario_requerido: boolean;
  formulario_nombre?: string; // "Formulario" field
  tiempo_respuesta: string; // "Tiempo de respuesta" field
  tiene_costo: boolean;
  costo_descripcion?: string; // "¿Tiene pago?" field
  url_suit?: string; // "Visualización trámite en el SUIT"
  url_gov_co?: string; // "Visualización trámite en GOV.CO"

  // Search and AI optimization
  palabras_clave: string[];
  sinonimos: string[];
  preguntas_frecuentes?: Array<{
    pregunta: string;
    respuesta: string;
  }>;
  tiempo_estimado_minutos?: number;
  dificultad_percibida?: number; // 1-5
  popularidad_score: number;
  automatizable: boolean;
  porcentaje_automatizacion: number;
  contenido_embedding?: number[]; // Vector embeddings
  ultima_actualizacion_contenido?: Date;
  estado: 'activo' | 'inactivo' | 'en_revision';
  fecha_publicacion: Date;
  created_at: Date;
  updated_at: Date;
}

interface FAQ {
  id: string;
  dependencia_id: string;
  subdependencia_id?: string;
  faq_tema_id: string;

  // FAQ Content (matching faqs JSON structure)
  codigo_dependencia: string;
  codigo_subdependencia?: string;
  pregunta: string; // "pregunta" field
  respuesta: string; // "respuesta" field
  palabras_clave: string[]; // "palabras_clave" array

  // AI Embeddings for semantic search
  contenido_embedding?: number[]; // OpenAI embedding

  // Metadata
  popularidad: number;
  activa: boolean;
  created_at: Date;
  updated_at: Date;
}
```

**Relationships:**

- Belongs to Dependencia (n:1)
- Has many ConversacionesIA (1:n)

### ConversacionIA (AI Conversation)

**Purpose:** Records all AI interactions with citizens, enabling continuous improvement of AI responses, tracking resolution rates, and providing audit trails for government accountability.

**Key Attributes:**

- id: UUID - Unique identifier
- ciudadano_id: UUID - Reference to citizen
- sesion_id: string - Session identifier
- intencion_detectada: string - Detected intent
- pregunta_original: text - Original citizen question
- respuesta_ia: text - AI response
- confianza_respuesta: decimal - AI confidence score
- feedback_ciudadano: number - Citizen feedback (1-5)
- resuelto: boolean - Whether query was resolved

**TypeScript Interface:**

```typescript
interface ConversacionIA {
  id: string;
  ciudadano_id?: string;
  sesion_id: string;
  intencion_detectada?: string;
  categoria_servicio?: string;
  nivel_complejidad?: number; // 1-5
  pregunta_original: string;
  pregunta_procesada?: string;
  respuesta_ia?: string;
  fuentes_utilizadas?: Array<{
    tipo: string;
    id: string;
    titulo: string;
    relevancia: number;
  }>;
  confianza_respuesta?: number; // 0.00-1.00
  feedback_ciudadano?: number; // 1-5
  tiempo_respuesta_ms?: number;
  escalado_humano: boolean;
  resuelto: boolean;
  modelo_ia_usado: string;
  tokens_consumidos?: number;
  costo_estimado?: number;
  created_at: Date;
}
```

**Relationships:**

- Belongs to Ciudadano (n:1)
- References ServicioCiudadano (n:n through fuentes_utilizadas)

### KnowledgeBase (Knowledge Base)

**Purpose:** Structured knowledge repository for AI training and responses, containing verified government information, FAQs, and official procedures with semantic search capabilities.

**Key Attributes:**

- id: UUID - Unique identifier
- pregunta_original: text - Original question
- variaciones_pregunta: string[] - Question variations
- respuesta_oficial: text - Official verified answer
- embedding_pregunta: vector - Question embeddings
- embedding_respuesta: vector - Answer embeddings
- categoria: string - Content category
- verificado_por: string - Verifying official

**TypeScript Interface:**

```typescript
interface KnowledgeBase {
  id: string;
  pregunta_original: string;
  variaciones_pregunta: string[];
  respuesta_oficial: string;
  embedding_pregunta?: number[];
  embedding_respuesta?: number[];
  categoria?: string;
  popularidad: number;
  ultima_actualizacion: Date;
  verificado_por?: string;
  fuente_oficial?: string;
  activo: boolean;
  created_at: Date;
  updated_at: Date;
}
```

**Relationships:**

- Referenced by ConversacionesIA for response generation

## REST API Spec

```yaml
openapi: 3.0.0
info:
  title: Chia-Next AI-First Citizen Services API
  version: 1.0.0
  description: RESTful API for AI-powered government citizen services platform
servers:
  - url: https://chia-next.vercel.app/api
    description: Production API
  - url: http://localhost:3000/api
    description: Development API

paths:
  /auth/login:
    post:
      summary: Authenticate citizen
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
      responses:
        '200':
          description: Authentication successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    $ref: '#/components/schemas/Ciudadano'
                  session:
                    type: object

  /chat:
    post:
      summary: Send message to AI chatbot
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                message:
                  type: string
                session_id:
                  type: string
                context:
                  type: object
      responses:
        '200':
          description: AI response
          content:
            application/json:
              schema:
                type: object
                properties:
                  response:
                    type: string
                  confidence:
                    type: number
                  sources:
                    type: array
                    items:
                      type: object
                  session_id:
                    type: string

  /search:
    get:
      summary: Semantic search for services
      parameters:
        - name: q
          in: query
          required: true
          schema:
            type: string
        - name: limit
          in: query
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: Search results
          content:
            application/json:
              schema:
                type: object
                properties:
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/ServicioCiudadano'
                  total:
                    type: integer

  /servicios:
    get:
      summary: List citizen services
      parameters:
        - name: dependencia_id
          in: query
          schema:
            type: string
        - name: tipo
          in: query
          schema:
            type: string
            enum: [tramite, consulta, certificado, permiso]
      responses:
        '200':
          description: List of services
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ServicioCiudadano'

  /ciudadano/perfil:
    get:
      summary: Get citizen profile
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Citizen profile
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Ciudadano'

    put:
      summary: Update citizen profile
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Ciudadano'
      responses:
        '200':
          description: Profile updated

  /dependencias:
    get:
      summary: List government departments
      responses:
        '200':
          description: List of departments
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Dependencia'

  /dependencias/{codigo_dependencia}/subdependencias:
    get:
      summary: List subdepartments for a department
      parameters:
        - name: codigo_dependencia
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: List of subdepartments
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Subdependencia'

  /opa:
    get:
      summary: Get OPA routing information
      parameters:
        - name: dependencia_id
          in: query
          schema:
            type: string
        - name: subdependencia_id
          in: query
          schema:
            type: string
      responses:
        '200':
          description: OPA routing data
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/OPA'

  /faqs:
    get:
      summary: Search FAQs with semantic search
      parameters:
        - name: q
          in: query
          required: true
          schema:
            type: string
        - name: dependencia_id
          in: query
          schema:
            type: string
        - name: tema
          in: query
          schema:
            type: string
        - name: limit
          in: query
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: FAQ search results
          content:
            application/json:
              schema:
                type: object
                properties:
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/FAQ'
                  total:
                    type: integer

  /tramites:
    get:
      summary: List government procedures (tramites)
      parameters:
        - name: dependencia_id
          in: query
          schema:
            type: string
        - name: subdependencia_id
          in: query
          schema:
            type: string
        - name: tipo_servicio
          in: query
          schema:
            type: string
            enum: [tramite, consulta, certificado, permiso, impuesto, licencia]
        - name: tiene_costo
          in: query
          schema:
            type: boolean
      responses:
        '200':
          description: List of procedures
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ServicioCiudadano'

components:
  schemas:
    Ciudadano:
      type: object
      properties:
        id:
          type: string
          format: uuid
        nombre_completo:
          type: string
        email:
          type: string
          format: email
        telefono:
          type: string
        preferencias_comunicacion:
          type: object
        nivel_asistencia:
          type: string
          enum: [basico, intermedio, avanzado]

    ServicioCiudadano:
      type: object
      properties:
        id:
          type: string
          format: uuid
        dependencia_id:
          type: string
          format: uuid
        subdependencia_id:
          type: string
          format: uuid
        nombre:
          type: string
        codigo_dependencia:
          type: string
        codigo_subdependencia:
          type: string
        formulario_requerido:
          type: boolean
        formulario_nombre:
          type: string
        tiempo_respuesta:
          type: string
        tiene_costo:
          type: boolean
        costo_descripcion:
          type: string
        url_suit:
          type: string
        url_gov_co:
          type: string
        tipo_servicio:
          type: string
          enum: [tramite, consulta, certificado, permiso, impuesto, licencia]
        tiempo_estimado_minutos:
          type: integer
        popularidad_score:
          type: integer

    Dependencia:
      type: object
      properties:
        id:
          type: string
          format: uuid
        codigo_dependencia:
          type: string
        nombre:
          type: string
        sigla:
          type: string
        descripcion:
          type: string
        activo:
          type: boolean

    Subdependencia:
      type: object
      properties:
        id:
          type: string
          format: uuid
        dependencia_id:
          type: string
          format: uuid
        codigo_subdependencia:
          type: string
        nombre:
          type: string
        sigla:
          type: string
        activo:
          type: boolean

    OPA:
      type: object
      properties:
        id:
          type: string
          format: uuid
        dependencia_id:
          type: string
          format: uuid
        subdependencia_id:
          type: string
          format: uuid
        codigo_opa:
          type: string
        descripcion:
          type: string
        activa:
          type: boolean

    FAQ:
      type: object
      properties:
        id:
          type: string
          format: uuid
        dependencia_id:
          type: string
          format: uuid
        subdependencia_id:
          type: string
          format: uuid
        codigo_dependencia:
          type: string
        codigo_subdependencia:
          type: string
        pregunta:
          type: string
        respuesta:
          type: string
        palabras_clave:
          type: array
          items:
            type: string
        popularidad:
          type: integer
        activa:
          type: boolean

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
```

## Components

### AI Service Layer

**Responsibility:** Manages all AI interactions including GPT-4 conversations, embedding generation, semantic search, and response optimization for government-specific contexts.

**Key Interfaces:**

- `processConversation(message, context)` - Handle citizen conversations
- `generateEmbeddings(content)` - Create vector embeddings for search
- `semanticSearch(query, filters)` - Perform semantic search across services
- `optimizeResponse(response, userProfile)` - Personalize AI responses

**Dependencies:** OpenAI API, pgvector database, user profile service

**Technology Stack:** TypeScript, OpenAI SDK, custom prompt engineering, vector similarity algorithms

### Authentication & Authorization Service

**Responsibility:** Handles citizen and administrator authentication, session management, role-based access control, and integration with government identity systems.

**Key Interfaces:**

- `authenticateUser(credentials)` - User login
- `authorizeAccess(user, resource)` - Permission checking
- `manageSession(sessionId)` - Session lifecycle
- `integrateGovID(govIdToken)` - Government ID integration

**Dependencies:** Supabase Auth, Row Level Security policies

**Technology Stack:** Supabase Auth, JWT tokens, RLS policies, TypeScript

### Data Access Layer

**Responsibility:** Abstracts database operations, implements repository patterns, manages data consistency, and provides type-safe database interactions across all entities.

**Key Interfaces:**

- `CiudadanoRepository` - Citizen data operations
- `ServicioRepository` - Service management
- `ConversacionRepository` - AI conversation logging
- `KnowledgeBaseRepository` - Knowledge management

**Dependencies:** Supabase client, PostgreSQL, pgvector

**Technology Stack:** Supabase client, PostgreSQL, TypeScript, custom repository patterns

### Notification Service

**Responsibility:** Manages multi-channel notifications (email, SMS, push) for process updates, reminders, and system communications with citizen preference respect.

**Key Interfaces:**

- `sendNotification(userId, message, channels)` - Send notifications
- `scheduleReminder(userId, reminder, date)` - Schedule future notifications
- `updatePreferences(userId, preferences)` - Manage notification preferences
- `trackDelivery(notificationId)` - Monitor delivery status

**Dependencies:** Email service, SMS provider, push notification service

**Technology Stack:** Supabase Edge Functions, third-party notification APIs, TypeScript

### Search & Discovery Service

**Responsibility:** Provides intelligent search capabilities combining semantic search, traditional text search, and AI-powered query understanding for optimal service discovery.

**Key Interfaces:**

- `hybridSearch(query, filters)` - Combined semantic and text search
- `suggestServices(userProfile, context)` - Personalized recommendations
- `indexContent(content, metadata)` - Content indexing
- `analyzeQuery(query)` - Query intent analysis

**Dependencies:** pgvector, PostgreSQL full-text search, AI Service Layer

**Technology Stack:** PostgreSQL, pgvector, custom search algorithms, TypeScript

### Component Diagrams

```mermaid
graph TB
    subgraph "Frontend Components"
        UI[React Components]
        STATE[Zustand Store]
        QUERY[React Query]
    end

    subgraph "API Layer"
        API[Next.js API Routes]
        MW[Middleware]
    end

    subgraph "Service Layer"
        AI[AI Service]
        AUTH[Auth Service]
        DATA[Data Access Layer]
        NOTIF[Notification Service]
        SEARCH[Search Service]
    end

    subgraph "Data Layer"
        DB[(PostgreSQL)]
        VECTOR[(pgvector)]
        STORAGE[Supabase Storage]
    end

    subgraph "External Services"
        OPENAI[OpenAI API]
        GOV[Government APIs]
        EMAIL[Email Service]
    end

    UI --> STATE
    UI --> QUERY
    QUERY --> API

    API --> MW
    MW --> AUTH
    API --> AI
    API --> DATA
    API --> NOTIF
    API --> SEARCH

    AI --> OPENAI
    AI --> VECTOR
    DATA --> DB
    NOTIF --> EMAIL
    SEARCH --> VECTOR
    SEARCH --> DB

    AUTH --> DB
    DATA --> STORAGE
    API --> GOV
```

## External APIs

### OpenAI API

- **Purpose:** AI conversation processing, text embeddings generation, and semantic understanding for citizen queries
- **Documentation:** https://platform.openai.com/docs/api-reference
- **Base URL(s):** `https://api.openai.com/v1`
- **Authentication:** Bearer token with API key
- **Rate Limits:** 3,500 requests per minute (GPT-4), 3,000 requests per minute (embeddings)

**Key Endpoints Used:**

- `POST /chat/completions` - GPT-4 conversations for citizen assistance
- `POST /embeddings` - Generate embeddings for semantic search
- `POST /moderations` - Content moderation for user inputs

**Integration Notes:** Implement retry logic, cost monitoring, and response caching. Use streaming for real-time chat responses. Implement fallback to cached responses during outages.

### Government Identity API (Future Integration)

- **Purpose:** Integration with national identity verification systems for secure citizen authentication
- **Documentation:** [To be provided by government IT department]
- **Base URL(s):** [Government-specific endpoint]
- **Authentication:** Government-issued certificates and API keys
- **Rate Limits:** [To be determined based on government specifications]

**Key Endpoints Used:**

- `POST /verify-identity` - Verify citizen identity documents
- `GET /citizen-info` - Retrieve verified citizen information
- `POST /validate-document` - Validate government-issued documents

**Integration Notes:** Requires government security clearance, encrypted communications, and compliance with national data protection regulations. Implementation planned for Phase 2.

## Core Workflows

### Citizen AI Consultation Workflow

```mermaid
sequenceDiagram
    participant C as Citizen
    participant UI as Web Interface
    participant API as API Layer
    participant AI as AI Service
    participant DB as Database
    participant OpenAI as OpenAI API

    C->>UI: Types question
    UI->>API: POST /chat
    API->>AI: processConversation()
    AI->>DB: Search knowledge base
    DB-->>AI: Relevant content
    AI->>OpenAI: Generate response
    OpenAI-->>AI: AI response
    AI->>DB: Log conversation
    AI-->>API: Formatted response
    API-->>UI: Response + sources
    UI-->>C: Display answer

    Note over C,OpenAI: Real-time streaming for better UX

    C->>UI: Provides feedback
    UI->>API: POST /feedback
    API->>DB: Update conversation
```

### Service Discovery and Search Workflow

```mermaid
sequenceDiagram
    participant C as Citizen
    participant UI as Web Interface
    participant API as Search API
    participant SEARCH as Search Service
    participant DB as PostgreSQL
    participant VECTOR as pgvector

    C->>UI: Enters search query
    UI->>API: GET /search?q=query
    API->>SEARCH: hybridSearch()

    par Semantic Search
        SEARCH->>VECTOR: Vector similarity search
        VECTOR-->>SEARCH: Semantic results
    and Text Search
        SEARCH->>DB: Full-text search
        DB-->>SEARCH: Text results
    end

    SEARCH->>SEARCH: Combine and rank results
    SEARCH-->>API: Unified results
    API-->>UI: Search results
    UI-->>C: Display services

    C->>UI: Clicks service
    UI->>API: GET /servicios/{id}
    API->>DB: Fetch service details
    DB-->>API: Service information
    API-->>UI: Service details
    UI-->>C: Show service page
```

### User Authentication and Profile Setup

```mermaid
sequenceDiagram
    participant C as Citizen
    participant UI as Web Interface
    participant API as Auth API
    participant AUTH as Auth Service
    participant DB as Database
    participant SUPABASE as Supabase Auth

    C->>UI: Clicks "Iniciar Sesión"
    UI->>API: POST /auth/login
    API->>AUTH: authenticateUser()
    AUTH->>SUPABASE: Verify credentials
    SUPABASE-->>AUTH: Auth result

    alt First time user
        AUTH->>DB: Create citizen profile
        DB-->>AUTH: Profile created
        AUTH-->>API: New user + profile
        API-->>UI: Redirect to onboarding
        UI-->>C: Show welcome flow
    else Returning user
        AUTH->>DB: Fetch citizen profile
        DB-->>AUTH: Existing profile
        AUTH-->>API: User + profile
        API-->>UI: User session
        UI-->>C: Show dashboard
    end
```

## Database Schema

```sql
-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "vector";

-- Government Department Structure
CREATE TABLE dependencias (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    codigo_dependencia VARCHAR(10) UNIQUE NOT NULL, -- e.g., "000", "010", "030"
    nombre VARCHAR(255) NOT NULL,
    sigla VARCHAR(20),
    descripcion TEXT,
    nivel_jerarquico INTEGER DEFAULT 1,
    contacto_ciudadano JSONB DEFAULT '{}',
    horarios_atencion JSONB DEFAULT '{}',
    servicios_destacados TEXT[] DEFAULT '{}',
    activo BOOLEAN DEFAULT true,
    metadata_seo JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Sub-departments
CREATE TABLE subdependencias (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    dependencia_id UUID REFERENCES dependencias(id) ON DELETE CASCADE,
    codigo_subdependencia VARCHAR(10) NOT NULL, -- e.g., "001", "011", "031"
    nombre VARCHAR(255) NOT NULL,
    sigla VARCHAR(20),
    descripcion TEXT,
    contacto_ciudadano JSONB DEFAULT '{}',
    horarios_atencion JSONB DEFAULT '{}',
    activo BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(dependencia_id, codigo_subdependencia)
);

-- OPA (Orientación de Peticiones y Asuntos) - Service routing system
CREATE TABLE opa (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    dependencia_id UUID REFERENCES dependencias(id) ON DELETE CASCADE,
    subdependencia_id UUID REFERENCES subdependencias(id) ON DELETE CASCADE,
    codigo_opa VARCHAR(10) NOT NULL,
    descripcion TEXT NOT NULL,
    activa BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- FAQ Topics for hierarchical organization
CREATE TABLE faq_temas (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    dependencia_id UUID REFERENCES dependencias(id) ON DELETE CASCADE,
    subdependencia_id UUID REFERENCES subdependencias(id) ON DELETE CASCADE,
    tema VARCHAR(255) NOT NULL,
    descripcion TEXT,
    activo BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Citizen Digital Profile
CREATE TABLE ciudadanos (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    auth_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,

    -- Personal Information
    nombre_completo VARCHAR(255),
    identificacion_nacional VARCHAR(50) UNIQUE,
    email VARCHAR(255),
    telefono VARCHAR(20),

    -- Experience Preferences
    preferencias_comunicacion JSONB DEFAULT '{"email": true, "sms": false}',
    idioma_preferido VARCHAR(10) DEFAULT 'es',
    nivel_asistencia VARCHAR(20) DEFAULT 'intermedio',

    -- Usage Context
    primera_visita TIMESTAMPTZ DEFAULT NOW(),
    ultima_actividad TIMESTAMPTZ DEFAULT NOW(),
    sesiones_completadas INTEGER DEFAULT 0,
    satisfaccion_promedio DECIMAL(3,2),
    perfil_usuario VARCHAR(20) DEFAULT 'nuevo',

    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT valid_nivel_asistencia CHECK (nivel_asistencia IN ('basico', 'intermedio', 'avanzado')),
    CONSTRAINT valid_perfil_usuario CHECK (perfil_usuario IN ('nuevo', 'recurrente', 'power_user', 'necesita_ayuda'))
);

-- Citizen Services with AI Optimization (Based on tramites_chia_optimo.json)
CREATE TABLE servicios_ciudadanos (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    dependencia_id UUID REFERENCES dependencias(id) ON DELETE CASCADE,
    subdependencia_id UUID REFERENCES subdependencias(id) ON DELETE CASCADE,

    -- Service Information (matching JSON structure)
    nombre VARCHAR(500) NOT NULL, -- "Nombre" field from JSON
    descripcion_corta TEXT,
    descripcion_completa TEXT,
    tipo_servicio VARCHAR(20) NOT NULL,

    -- Tramite-specific fields from JSON
    codigo_dependencia VARCHAR(10) NOT NULL,
    codigo_subdependencia VARCHAR(10),
    formulario_requerido BOOLEAN DEFAULT false,
    formulario_nombre VARCHAR(255), -- "Formulario" field
    tiempo_respuesta VARCHAR(100), -- "Tiempo de respuesta" field
    tiene_costo BOOLEAN DEFAULT false,
    costo_descripcion TEXT, -- "¿Tiene pago?" field
    url_suit VARCHAR(500), -- "Visualización trámite en el SUIT"
    url_gov_co VARCHAR(500), -- "Visualización trámite en GOV.CO"

    -- Search Optimization
    palabras_clave TEXT[] DEFAULT '{}',
    sinonimos TEXT[] DEFAULT '{}',
    preguntas_frecuentes JSONB DEFAULT '[]',

    -- AI and Analytics
    contenido_embedding vector(1536), -- OpenAI embedding dimension
    tiempo_estimado_minutos INTEGER,
    dificultad_percibida INTEGER CHECK (dificultad_percibida BETWEEN 1 AND 5),
    popularidad_score INTEGER DEFAULT 0,
    automatizable BOOLEAN DEFAULT false,
    porcentaje_automatizacion INTEGER DEFAULT 0,

    -- Content Management
    ultima_actualizacion_contenido TIMESTAMPTZ,
    estado VARCHAR(20) DEFAULT 'activo',
    fecha_publicacion TIMESTAMPTZ DEFAULT NOW(),

    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT valid_tipo_servicio CHECK (tipo_servicio IN ('tramite', 'consulta', 'certificado', 'permiso', 'impuesto', 'licencia')),
    CONSTRAINT valid_estado CHECK (estado IN ('activo', 'inactivo', 'en_revision'))
);

-- FAQ System (Based on faqs_chia_estructurado.json)
CREATE TABLE faqs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    dependencia_id UUID REFERENCES dependencias(id) ON DELETE CASCADE,
    subdependencia_id UUID REFERENCES subdependencias(id) ON DELETE CASCADE,
    faq_tema_id UUID REFERENCES faq_temas(id) ON DELETE CASCADE,

    -- FAQ Content (matching JSON structure)
    codigo_dependencia VARCHAR(10) NOT NULL,
    codigo_subdependencia VARCHAR(10),
    pregunta TEXT NOT NULL, -- "pregunta" field
    respuesta TEXT NOT NULL, -- "respuesta" field
    palabras_clave TEXT[] DEFAULT '{}', -- "palabras_clave" array

    -- AI Embeddings for semantic search
    contenido_embedding vector(1536), -- OpenAI embedding

    -- Metadata
    popularidad INTEGER DEFAULT 0,
    activa BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- AI Conversation Logging
CREATE TABLE conversaciones_ia (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ciudadano_id UUID REFERENCES ciudadanos(id) ON DELETE SET NULL,
    sesion_id VARCHAR(255) NOT NULL,

    -- Intent and Classification
    intencion_detectada VARCHAR(255),
    categoria_servicio VARCHAR(100),
    nivel_complejidad INTEGER CHECK (nivel_complejidad BETWEEN 1 AND 5),

    -- Conversation Content
    pregunta_original TEXT NOT NULL,
    pregunta_procesada TEXT,
    respuesta_ia TEXT,
    fuentes_utilizadas JSONB DEFAULT '[]',

    -- AI Metrics
    confianza_respuesta DECIMAL(5,4) CHECK (confianza_respuesta BETWEEN 0 AND 1),
    tiempo_respuesta_ms INTEGER,
    modelo_ia_usado VARCHAR(50) DEFAULT 'gpt-4',
    tokens_consumidos INTEGER,
    costo_estimado DECIMAL(10,6),

    -- Feedback and Resolution
    feedback_ciudadano INTEGER CHECK (feedback_ciudadano BETWEEN 1 AND 5),
    escalado_humano BOOLEAN DEFAULT false,
    resuelto BOOLEAN DEFAULT false,

    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Knowledge Base for AI Training
CREATE TABLE knowledge_base (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    -- Content
    pregunta_original TEXT NOT NULL,
    variaciones_pregunta TEXT[] DEFAULT '{}',
    respuesta_oficial TEXT NOT NULL,

    -- AI Embeddings
    embedding_pregunta vector(1536),
    embedding_respuesta vector(1536),

    -- Metadata
    categoria VARCHAR(100),
    popularidad INTEGER DEFAULT 0,
    ultima_actualizacion TIMESTAMPTZ DEFAULT NOW(),
    verificado_por VARCHAR(255),
    fuente_oficial VARCHAR(500),
    activo BOOLEAN DEFAULT true,

    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for Performance
CREATE INDEX idx_servicios_embedding ON servicios_ciudadanos USING ivfflat (contenido_embedding vector_cosine_ops);
CREATE INDEX idx_faqs_embedding ON faqs USING ivfflat (contenido_embedding vector_cosine_ops);
CREATE INDEX idx_knowledge_pregunta_embedding ON knowledge_base USING ivfflat (embedding_pregunta vector_cosine_ops);
CREATE INDEX idx_knowledge_respuesta_embedding ON knowledge_base USING ivfflat (embedding_respuesta vector_cosine_ops);
CREATE INDEX idx_conversaciones_sesion ON conversaciones_ia(sesion_id);
CREATE INDEX idx_conversaciones_ciudadano ON conversaciones_ia(ciudadano_id);
CREATE INDEX idx_servicios_tipo ON servicios_ciudadanos(tipo_servicio);
CREATE INDEX idx_servicios_dependencia ON servicios_ciudadanos(dependencia_id);
CREATE INDEX idx_servicios_subdependencia ON servicios_ciudadanos(subdependencia_id);
CREATE INDEX idx_servicios_codigo_dep ON servicios_ciudadanos(codigo_dependencia);
CREATE INDEX idx_servicios_codigo_subdep ON servicios_ciudadanos(codigo_subdependencia);

-- New table indexes
CREATE INDEX idx_dependencias_codigo ON dependencias(codigo_dependencia);
CREATE INDEX idx_subdependencias_codigo ON subdependencias(codigo_subdependencia);
CREATE INDEX idx_subdependencias_dependencia ON subdependencias(dependencia_id);
CREATE INDEX idx_opa_dependencia ON opa(dependencia_id);
CREATE INDEX idx_opa_subdependencia ON opa(subdependencia_id);
CREATE INDEX idx_faqs_dependencia ON faqs(dependencia_id);
CREATE INDEX idx_faqs_subdependencia ON faqs(subdependencia_id);
CREATE INDEX idx_faqs_tema ON faqs(faq_tema_id);
CREATE INDEX idx_faqs_codigo_dep ON faqs(codigo_dependencia);
CREATE INDEX idx_faqs_codigo_subdep ON faqs(codigo_subdependencia);
CREATE INDEX idx_faq_temas_dependencia ON faq_temas(dependencia_id);
CREATE INDEX idx_faq_temas_subdependencia ON faq_temas(subdependencia_id);

-- Full-text search indexes
CREATE INDEX idx_servicios_search ON servicios_ciudadanos USING gin(to_tsvector('spanish', nombre || ' ' || COALESCE(descripcion_completa, '')));
CREATE INDEX idx_faqs_search ON faqs USING gin(to_tsvector('spanish', pregunta || ' ' || respuesta));
CREATE INDEX idx_knowledge_search ON knowledge_base USING gin(to_tsvector('spanish', pregunta_original || ' ' || respuesta_oficial));

-- GIN indexes for array fields
CREATE INDEX idx_faqs_palabras_clave ON faqs USING gin(palabras_clave);
CREATE INDEX idx_servicios_palabras_clave ON servicios_ciudadanos USING gin(palabras_clave);
```

## Row Level Security (RLS) Policies

```sql
-- Enable RLS on all tables
ALTER TABLE ciudadanos ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversaciones_ia ENABLE ROW LEVEL SECURITY;
ALTER TABLE servicios_ciudadanos ENABLE ROW LEVEL SECURITY;
ALTER TABLE dependencias ENABLE ROW LEVEL SECURITY;
ALTER TABLE subdependencias ENABLE ROW LEVEL SECURITY;
ALTER TABLE opa ENABLE ROW LEVEL SECURITY;
ALTER TABLE faq_temas ENABLE ROW LEVEL SECURITY;
ALTER TABLE faqs ENABLE ROW LEVEL SECURITY;
ALTER TABLE knowledge_base ENABLE ROW LEVEL SECURITY;

-- Citizen data access policies
CREATE POLICY "Citizens can view own profile" ON ciudadanos
    FOR SELECT USING (auth.uid() = auth_id);

CREATE POLICY "Citizens can update own profile" ON ciudadanos
    FOR UPDATE USING (auth.uid() = auth_id);

-- Conversation access policies
CREATE POLICY "Citizens can view own conversations" ON conversaciones_ia
    FOR SELECT USING (
        ciudadano_id IN (
            SELECT id FROM ciudadanos WHERE auth_id = auth.uid()
        )
    );

CREATE POLICY "System can insert conversations" ON conversaciones_ia
    FOR INSERT WITH CHECK (true);

-- Public service information
CREATE POLICY "Anyone can view active services" ON servicios_ciudadanos
    FOR SELECT USING (estado = 'activo');

CREATE POLICY "Anyone can view active departments" ON dependencias
    FOR SELECT USING (activo = true);

CREATE POLICY "Anyone can view active knowledge base" ON knowledge_base
    FOR SELECT USING (activo = true);

-- New table policies
CREATE POLICY "Anyone can view active subdependencies" ON subdependencias
    FOR SELECT USING (activo = true);

CREATE POLICY "Anyone can view active OPA" ON opa
    FOR SELECT USING (activa = true);

CREATE POLICY "Anyone can view active FAQ topics" ON faq_temas
    FOR SELECT USING (activo = true);

CREATE POLICY "Anyone can view active FAQs" ON faqs
    FOR SELECT USING (activa = true);

-- Admin policies (for future admin panel)
CREATE POLICY "Admins can manage all data" ON ciudadanos
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM auth.users
            WHERE auth.uid() = id
            AND raw_user_meta_data->>'role' = 'admin'
        )
    );
```

## Security and Performance

### Security Measures

**Authentication & Authorization:**
- Supabase Auth with JWT tokens for session management
- Row Level Security (RLS) policies for data isolation
- Multi-factor authentication support for sensitive operations
- Government ID integration ready for Phase 2

**Data Protection:**
- End-to-end encryption for sensitive citizen data
- GDPR/CCPA compliance with data retention policies
- Audit logging for all data access and modifications
- Regular security assessments and penetration testing

**API Security:**
- Rate limiting on all API endpoints (100 requests/minute per user)
- Input validation and sanitization for all user inputs
- CORS policies restricting cross-origin requests
- API key rotation and monitoring for external services

**Code Security (Ultracite Integration):**
- Automated detection of hardcoded secrets and API keys
- Prevention of XSS vulnerabilities through secure coding patterns
- Enforcement of secure link handling (`rel="noopener"` for external links)
- ARIA and accessibility compliance for government standards
- TypeScript strict mode enforcement preventing runtime security issues

**Infrastructure Security:**
- HTTPS enforcement with TLS 1.3
- Content Security Policy (CSP) headers
- Regular dependency updates and vulnerability scanning
- Secure environment variable management

### Performance Optimizations

**Frontend Performance:**
- Next.js App Router with automatic code splitting
- Image optimization with Next.js Image component
- Static generation for public pages (services, departments)
- Edge caching via Vercel CDN with 99.9% uptime SLA

**Database Performance:**
- Vector indexes (ivfflat) for semantic search queries
- Full-text search indexes for traditional search
- Connection pooling via Supabase (max 60 connections)
- Query optimization with proper indexing strategy

**AI Performance:**
- Response caching for common queries (Redis-compatible)
- Streaming responses for real-time chat experience
- Embedding pre-computation for frequently accessed content
- Cost optimization with usage monitoring and alerts

**Monitoring & Observability:**
- Real-time performance monitoring via Vercel Analytics
- Database performance metrics via Supabase Dashboard
- Custom metrics for AI response times and accuracy
- Error tracking and alerting for critical failures

### Performance Targets

| Metric | Target | Measurement |
|--------|--------|-------------|
| Page Load Time | < 2 seconds | Core Web Vitals LCP |
| AI Response Time | < 3 seconds | Time to first token |
| Search Response | < 500ms | API response time |
| Database Queries | < 100ms | Average query execution |
| Uptime | 99.9% | Monthly availability |
| Core Web Vitals | All Green | Google PageSpeed Insights |

## Development Workflow

### Git Workflow

**Branching Strategy:** GitFlow with feature branches
- `main` - Production-ready code
- `develop` - Integration branch for features
- `feature/*` - Individual feature development
- `hotfix/*` - Critical production fixes
- `release/*` - Release preparation

**Commit Standards:** Conventional Commits
- `feat:` - New features
- `fix:` - Bug fixes
- `docs:` - Documentation updates
- `style:` - Code formatting
- `refactor:` - Code restructuring
- `test:` - Test additions/updates
- `chore:` - Maintenance tasks

### Code Review Process

**Review Requirements:**
- All code must be reviewed by at least one team member
- Automated tests must pass before merge
- Security review required for authentication/authorization changes
- Performance review required for database schema changes

**Review Checklist:**
- [ ] Code follows TypeScript best practices
- [ ] Tests cover new functionality
- [ ] Documentation updated if needed
- [ ] No hardcoded secrets or sensitive data
- [ ] Accessibility standards maintained
- [ ] Performance impact considered

### Local Development Setup

```bash
# Clone repository
git clone https://github.com/your-org/chia-next.git
cd chia-next

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local
# Edit .env.local with your Supabase and OpenAI credentials

# Start development server
npm run dev

# Run tests
npm test

# Run type checking
npm run type-check

# Run linting
npm run lint
```

### Code Quality with Ultracite

**Migration from ESLint to Ultracite - RECOMMENDED**

Chia-Next project is recommended to migrate from traditional ESLint to [Ultracite](https://www.ultracite.ai/migrate/eslint) for enhanced code quality, security, and government compliance.

**Why Ultracite for Government Digital Services:**

1. **Zero-Configuration Setup:**
   ```bash
   # Simple migration process
   npm install --save-dev @ultracite/eslint-config
   npx @ultracite/migrate-eslint
   ```

2. **Government-Critical Features:**
   - **Accessibility Compliance:** 30+ WCAG rules built-in (critical for government services)
   - **Security Best Practices:** Prevents hardcoded secrets, enforces secure patterns
   - **Next.js Native Support:** Optimized rules for Next.js 15 without additional plugins
   - **TypeScript Excellence:** Strict typing rules for government-grade reliability

3. **200+ Optimized Rules Including:**
   ```typescript
   // Accessibility (Government Requirement)
   - ARIA attributes validation
   - Semantic HTML enforcement
   - Screen reader compatibility

   // Security (Critical for Government)
   - No hardcoded API keys or secrets
   - Secure link handling (rel="noopener")
   - XSS prevention patterns

   // Next.js Optimization
   - Use next/image instead of <img>
   - Proper head management
   - Performance best practices

   // Modern JavaScript/TypeScript
   - Prefer const over let/var
   - Use optional chaining
   - Avoid any types
   ```

4. **Performance Benefits:**
   - **Faster linting:** Single optimized tool vs. multiple ESLint plugins
   - **Reduced dependencies:** Eliminates need for @next/eslint-config, @typescript-eslint, jsx-a11y
   - **Automatic updates:** Self-maintaining rule sets

5. **Migration Plan:**
   ```bash
   # 1. Install Ultracite
   npm install --save-dev @ultracite/eslint-config

   # 2. Auto-migrate existing ESLint config
   npx @ultracite/migrate-eslint

   # 3. Update package.json
   {
     "eslintConfig": {
       "extends": ["@ultracite"]
     }
   }

   # 4. Clean up old dependencies
   npm uninstall eslint-config-next @typescript-eslint/eslint-plugin eslint-plugin-react eslint-plugin-jsx-a11y

   # 5. Update CI/CD scripts (no changes needed - same npm run lint)
   ```

**Government Compliance Benefits:**
- **WCAG 2.1 AA Compliance:** Built-in accessibility rules
- **Security Standards:** Prevents common vulnerabilities
- **Code Consistency:** Enforces government coding standards
- **Audit Trail:** Better code quality metrics for compliance reporting

**Recommendation Status:** ✅ **APPROVED** - Migrate during next development cycle

### Environment Management

**Development Environment:**
- Local Supabase instance via Docker
- OpenAI API with development rate limits
- Hot reloading with Next.js dev server
- Local testing with Jest and Playwright

**Staging Environment:**
- Vercel preview deployments for feature branches
- Supabase staging project with production-like data
- Full CI/CD pipeline testing
- Performance testing with realistic data volumes

**Production Environment:**
- Vercel production deployment with custom domain
- Supabase production project with backups
- Monitoring and alerting configured
- Automated deployment from main branch

## Deployment Strategy

### Infrastructure as Code

**Vercel Configuration (`vercel.json`):**

```json
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/next"
    }
  ],
  "env": {
    "NEXT_PUBLIC_SUPABASE_URL": "@supabase-url",
    "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase-anon-key",
    "SUPABASE_SERVICE_ROLE_KEY": "@supabase-service-key",
    "OPENAI_API_KEY": "@openai-api-key"
  },
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        }
      ]
    }
  ]
}
```

**Supabase Configuration:**

```sql
-- Database setup script
-- Run via Supabase CLI: supabase db push

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "vector";

-- Create tables (schema from previous section)
-- Apply RLS policies
-- Create indexes
-- Seed initial data
```

### CI/CD Pipeline

**GitHub Actions Workflow (`.github/workflows/deploy.yml`):**

```yaml
name: Deploy to Vercel

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - run: npm ci
      - run: npm run type-check
      - run: npm run lint
      - run: npm test
      - run: npm run build

  deploy-preview:
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    needs: test
    steps:
      - uses: actions/checkout@v4
      - uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}

  deploy-production:
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    needs: test
    steps:
      - uses: actions/checkout@v4
      - uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
```

### Environment Configuration

**Environment Variables:**

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key
OPENAI_ORGANIZATION=your-org-id

# Application Configuration
NEXT_PUBLIC_APP_URL=https://chia-next.vercel.app
NEXT_PUBLIC_ENVIRONMENT=production

# Monitoring and Analytics
VERCEL_ANALYTICS_ID=your-analytics-id
```

### Deployment Checklist

**Pre-Deployment:**
- [ ] All tests passing
- [ ] Security review completed
- [ ] Performance benchmarks met
- [ ] Database migrations tested
- [ ] Environment variables configured
- [ ] Monitoring and alerting set up

**Post-Deployment:**
- [ ] Health checks passing
- [ ] Core functionality verified
- [ ] Performance metrics within targets
- [ ] Error rates within acceptable limits
- [ ] User acceptance testing completed
- [ ] Documentation updated

### Rollback Strategy

**Automated Rollback Triggers:**
- Error rate > 5% for 5 minutes
- Response time > 10 seconds for 3 minutes
- Core functionality failures detected
- Database connection failures

**Manual Rollback Process:**
1. Identify issue and impact scope
2. Execute Vercel rollback to previous deployment
3. Verify system stability
4. Communicate status to stakeholders
5. Investigate root cause
6. Plan fix and re-deployment

## Testing Strategy

### Testing Pyramid

**Unit Tests (70% of test suite):**
- Individual functions and components
- Business logic validation
- Data transformation utilities
- API endpoint handlers

**Integration Tests (20% of test suite):**
- Database operations
- External API integrations
- Authentication flows
- End-to-end user workflows

**End-to-End Tests (10% of test suite):**
- Critical user journeys
- Cross-browser compatibility
- Accessibility compliance
- Performance benchmarks

### Test Configuration

**Jest Configuration (`jest.config.js`):**

```javascript
const nextJest = require('next/jest')

const createJestConfig = nextJest({
  dir: './',
})

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testEnvironment: 'jest-environment-jsdom',
  collectCoverageFrom: [
    'app/**/*.{js,jsx,ts,tsx}',
    'lib/**/*.{js,jsx,ts,tsx}',
    '!**/*.d.ts',
    '!**/node_modules/**',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
}

module.exports = createJestConfig(customJestConfig)
```

**Playwright Configuration (`playwright.config.ts`):**

```typescript
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests/e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  },
});
```

### Test Examples

**Unit Test Example:**

```typescript
// tests/unit/ai-service.test.ts
import { processConversation } from '@/lib/ai-service';
import { mockOpenAI } from '@/tests/mocks/openai';

describe('AI Service', () => {
  beforeEach(() => {
    mockOpenAI.reset();
  });

  it('should process citizen conversation successfully', async () => {
    const mockResponse = {
      response: 'Para obtener su cédula, debe...',
      confidence: 0.95,
      sources: [{ id: '1', title: 'Trámite de Cédula' }]
    };

    mockOpenAI.mockResponse(mockResponse);

    const result = await processConversation(
      '¿Cómo obtengo mi cédula?',
      { userId: 'test-user' }
    );

    expect(result.response).toContain('cédula');
    expect(result.confidence).toBeGreaterThan(0.8);
    expect(result.sources).toHaveLength(1);
  });

  it('should handle low confidence responses', async () => {
    mockOpenAI.mockResponse({ confidence: 0.3 });

    const result = await processConversation(
      'Pregunta muy ambigua',
      { userId: 'test-user' }
    );

    expect(result.escalado_humano).toBe(true);
  });
});
```

**Integration Test Example:**

```typescript
// tests/integration/auth-flow.test.ts
import { createClient } from '@supabase/supabase-js';
import { testUser } from '@/tests/fixtures/users';

describe('Authentication Flow', () => {
  let supabase: ReturnType<typeof createClient>;

  beforeAll(() => {
    supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_ANON_KEY!
    );
  });

  it('should create citizen profile on first login', async () => {
    const { data: authData } = await supabase.auth.signUp({
      email: testUser.email,
      password: testUser.password,
    });

    expect(authData.user).toBeTruthy();

    // Check if citizen profile was created
    const { data: profile } = await supabase
      .from('ciudadanos')
      .select('*')
      .eq('auth_id', authData.user!.id)
      .single();

    expect(profile).toBeTruthy();
    expect(profile.perfil_usuario).toBe('nuevo');
  });
});
```

**E2E Test Example:**

```typescript
// tests/e2e/citizen-journey.spec.ts
import { test, expect } from '@playwright/test';

test('citizen can search and find services', async ({ page }) => {
  await page.goto('/');

  // Test search functionality
  await page.fill('[data-testid=search-input]', 'cédula de ciudadanía');
  await page.click('[data-testid=search-button]');

  // Verify search results
  await expect(page.locator('[data-testid=search-results]')).toBeVisible();
  await expect(page.locator('[data-testid=service-card]').first()).toContainText('Cédula');

  // Test service details
  await page.click('[data-testid=service-card]').first();
  await expect(page.locator('h1')).toContainText('Cédula de Ciudadanía');

  // Test AI chat
  await page.fill('[data-testid=chat-input]', '¿Qué documentos necesito?');
  await page.click('[data-testid=chat-send]');

  await expect(page.locator('[data-testid=ai-response]')).toBeVisible();
  await expect(page.locator('[data-testid=ai-response]')).toContainText('documentos');
});

test('accessibility compliance', async ({ page }) => {
  await page.goto('/');

  // Test keyboard navigation
  await page.keyboard.press('Tab');
  await expect(page.locator(':focus')).toBeVisible();

  // Test screen reader compatibility
  const searchInput = page.locator('[data-testid=search-input]');
  await expect(searchInput).toHaveAttribute('aria-label');

  // Test color contrast (would use axe-playwright in real implementation)
  // await injectAxe(page);
  // const results = await checkA11y(page);
  // expect(results.violations).toHaveLength(0);
});
```

## Coding Standards

### TypeScript Standards

**Type Definitions:**
- Use interfaces for object shapes, types for unions/primitives
- Prefer `unknown` over `any` for better type safety
- Use strict TypeScript configuration with all checks enabled
- Define return types for all public functions

**Example:**

```typescript
// Good
interface UserProfile {
  id: string;
  name: string;
  preferences: UserPreferences;
}

type UserRole = 'citizen' | 'admin' | 'operator';

function processUser(user: UserProfile): Promise<ProcessResult> {
  // Implementation
}

// Avoid
function processUser(user: any): any {
  // Implementation
}
```

### React Component Standards

**Component Structure:**
- Use functional components with hooks
- Implement proper prop validation with TypeScript
- Use React.memo for performance optimization when needed
- Follow single responsibility principle

**Example:**

```typescript
// components/ServiceCard.tsx
import React from 'react';
import { ServicioCiudadano } from '@/types/database';

interface ServiceCardProps {
  service: ServicioCiudadano;
  onSelect: (serviceId: string) => void;
  className?: string;
}

export const ServiceCard = React.memo<ServiceCardProps>(({
  service,
  onSelect,
  className = ''
}) => {
  const handleClick = () => {
    onSelect(service.id);
  };

  return (
    <div
      className={`service-card ${className}`}
      onClick={handleClick}
      role="button"
      tabIndex={0}
      aria-label={`Seleccionar servicio: ${service.nombre}`}
    >
      <h3>{service.nombre}</h3>
      <p>{service.descripcion_corta}</p>
      <div className="service-meta">
        <span>{service.tipo_servicio}</span>
        {service.tiempo_estimado_minutos && (
          <span>{service.tiempo_estimado_minutos} min</span>
        )}
      </div>
    </div>
  );
});

ServiceCard.displayName = 'ServiceCard';
```

### API Route Standards

**Structure and Error Handling:**

```typescript
// app/api/search/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { searchServices } from '@/lib/search-service';
import { validateSearchParams } from '@/lib/validation';
import { ApiError } from '@/lib/errors';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const limit = parseInt(searchParams.get('limit') || '10');

    // Validation
    const validationResult = validateSearchParams({ query, limit });
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid parameters', details: validationResult.errors },
        { status: 400 }
      );
    }

    // Business logic
    const results = await searchServices(query!, { limit });

    return NextResponse.json({
      results: results.services,
      total: results.total,
      query,
    });

  } catch (error) {
    console.error('Search API error:', error);

    if (error instanceof ApiError) {
      return NextResponse.json(
        { error: error.message },
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

### Database Query Standards

**Repository Pattern Implementation:**

```typescript
// lib/repositories/ciudadano-repository.ts
import { createClient } from '@/lib/supabase';
import { Ciudadano } from '@/types/database';

export class CiudadanoRepository {
  private supabase = createClient();

  async findById(id: string): Promise<Ciudadano | null> {
    const { data, error } = await this.supabase
      .from('ciudadanos')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw new Error(`Failed to fetch citizen: ${error.message}`);
    }

    return data;
  }

  async updateProfile(id: string, updates: Partial<Ciudadano>): Promise<Ciudadano> {
    const { data, error } = await this.supabase
      .from('ciudadanos')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update citizen profile: ${error.message}`);
    }

    return data;
  }

  async findByAuthId(authId: string): Promise<Ciudadano | null> {
    const { data, error } = await this.supabase
      .from('ciudadanos')
      .select('*')
      .eq('auth_id', authId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw new Error(`Failed to fetch citizen by auth ID: ${error.message}`);
    }

    return data;
  }
}
```

## Project Structure

### Monorepo Organization

```
chia-next/
├── apps/
│   ├── web/                    # Main citizen portal
│   │   ├── app/               # Next.js 15 App Router
│   │   │   ├── (auth)/        # Auth route group
│   │   │   ├── (dashboard)/   # Dashboard route group
│   │   │   ├── api/           # API routes
│   │   │   ├── globals.css    # Global styles
│   │   │   ├── layout.tsx     # Root layout
│   │   │   └── page.tsx       # Home page
│   │   ├── components/        # React components
│   │   │   ├── ui/            # Base UI components
│   │   │   ├── forms/         # Form components
│   │   │   ├── layout/        # Layout components
│   │   │   └── features/      # Feature-specific components
│   │   ├── lib/               # Utility libraries
│   │   │   ├── supabase.ts    # Supabase client
│   │   │   ├── openai.ts      # OpenAI client
│   │   │   ├── utils.ts       # General utilities
│   │   │   └── validations.ts # Form validations
│   │   ├── hooks/             # Custom React hooks
│   │   ├── types/             # TypeScript type definitions
│   │   ├── styles/            # Additional styles
│   │   └── public/            # Static assets
│   │
│   ├── admin/                 # Admin panel (Phase 2)
│   │   ├── app/
│   │   ├── components/
│   │   └── lib/
│   │
│   └── mobile/                # React Native app (Future)
│       ├── src/
│       ├── ios/
│       └── android/
│
├── packages/
│   ├── ui/                    # Shared UI components
│   │   ├── src/
│   │   │   ├── components/    # Reusable components
│   │   │   ├── styles/        # Shared styles
│   │   │   └── index.ts       # Package exports
│   │   ├── package.json
│   │   └── tsconfig.json
│   │
│   ├── database/              # Database utilities and types
│   │   ├── src/
│   │   │   ├── types/         # Database type definitions
│   │   │   ├── repositories/  # Data access layer
│   │   │   ├── migrations/    # Database migrations
│   │   │   └── seeds/         # Seed data
│   │   └── package.json
│   │
│   ├── ai/                    # AI service utilities
│   │   ├── src/
│   │   │   ├── openai/        # OpenAI integration
│   │   │   ├── embeddings/    # Embedding utilities
│   │   │   ├── prompts/       # AI prompts
│   │   │   └── types/         # AI-related types
│   │   └── package.json
│   │
│   ├── auth/                  # Authentication utilities
│   │   ├── src/
│   │   │   ├── supabase/      # Supabase auth integration
│   │   │   ├── middleware/    # Auth middleware
│   │   │   ├── hooks/         # Auth hooks
│   │   │   └── types/         # Auth types
│   │   └── package.json
│   │
│   └── config/                # Shared configuration
│       ├── ultracite/         # Ultracite linting configurations
│       ├── typescript/        # TypeScript configurations
│       ├── tailwind/          # Tailwind configurations
│       └── jest/              # Jest configurations
│
├── tests/
│   ├── unit/                  # Unit tests
│   ├── integration/           # Integration tests
│   ├── e2e/                   # End-to-end tests
│   ├── fixtures/              # Test data
│   ├── mocks/                 # Mock implementations
│   └── utils/                 # Test utilities
│
├── docs/
│   ├── api/                   # API documentation
│   ├── deployment/            # Deployment guides
│   ├── development/           # Development guides
│   └── architecture/          # Architecture documentation
│
├── scripts/
│   ├── setup.sh              # Development setup
│   ├── deploy.sh              # Deployment script
│   ├── seed-db.ts             # Database seeding
│   └── generate-types.ts      # Type generation
│
├── .github/
│   ├── workflows/             # GitHub Actions
│   ├── ISSUE_TEMPLATE/        # Issue templates
│   └── PULL_REQUEST_TEMPLATE.md
│
├── package.json               # Root package.json
├── turbo.json                 # Turbo configuration
├── tsconfig.json              # Root TypeScript config
├── .env.example               # Environment variables template
├── .gitignore
├── README.md
└── ARQUITECTURA.md            # This document
```

### Configuration Files

**Root Package.json:**

```json
{
  "name": "chia-next",
  "version": "1.0.0",
  "private": true,
  "workspaces": [
    "apps/*",
    "packages/*"
  ],
  "scripts": {
    "dev": "turbo run dev",
    "build": "turbo run build",
    "test": "turbo run test",
    "lint": "turbo run lint",
    "type-check": "turbo run type-check",
    "clean": "turbo run clean",
    "setup": "./scripts/setup.sh"
  },
  "devDependencies": {
    "@types/node": "^20.0.0",
    "@ultracite/eslint-config": "latest",
    "turbo": "^1.10.0",
    "typescript": "^5.3.0"
  },
  "eslintConfig": {
    "extends": ["@ultracite"]
  }
}
```

**Turbo Configuration (`turbo.json`):**

```json
{
  "$schema": "https://turbo.build/schema.json",
  "pipeline": {
    "build": {
      "dependsOn": ["^build"],
      "outputs": [".next/**", "dist/**"]
    },
    "dev": {
      "cache": false,
      "persistent": true
    },
    "test": {
      "dependsOn": ["^build"],
      "outputs": ["coverage/**"]
    },
    "lint": {
      "outputs": []
    },
    "type-check": {
      "dependsOn": ["^build"],
      "outputs": []
    },
    "clean": {
      "cache": false
    }
  }
}
```

**TypeScript Configuration (`tsconfig.json`):**

```json
{
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["dom", "dom.iterable", "ES6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./apps/web/*"],
      "@/ui": ["./packages/ui/src"],
      "@/database": ["./packages/database/src"],
      "@/ai": ["./packages/ai/src"],
      "@/auth": ["./packages/auth/src"]
    }
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts"
  ],
  "exclude": ["node_modules"]
}
```

**Tailwind Configuration (`tailwind.config.js`):**

```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './apps/web/app/**/*.{js,ts,jsx,tsx,mdx}',
    './apps/web/components/**/*.{js,ts,jsx,tsx,mdx}',
    './packages/ui/src/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        // Government brand colors
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
        },
        secondary: {
          50: '#f8fafc',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
        },
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    require('@headlessui/tailwindcss'),
  ],
}
```

## Implementation Roadmap

### Phase 1: AI Consultation Platform (Weeks 1-8)

**Week 1-2: Project Setup**
- [ ] Initialize Next.js 15 project with TypeScript
- [ ] Configure Supabase project and database schema
- [ ] Set up authentication with Supabase Auth
- [ ] Configure OpenAI API integration
- [ ] Set up development environment and tooling

**Week 3-4: Core AI Integration**
- [ ] Implement OpenAI GPT-4 API integration
- [ ] Create basic chat interface with streaming responses
- [ ] Implement knowledge base seeding system
- [ ] Set up vector embeddings for semantic search
- [ ] Create conversation logging system

**Week 5-6: Service Discovery**
- [ ] Build service catalog with search functionality
- [ ] Implement hybrid search (semantic + text)
- [ ] Create service detail pages
- [ ] Add service categorization and filtering
- [ ] Implement user preference learning

**Week 7-8: User Experience**
- [ ] Design and implement responsive UI
- [ ] Add accessibility features (WCAG 2.1 AA)
- [ ] Implement user onboarding flow
- [ ] Add feedback and rating system
- [ ] Performance optimization and testing

### Phase 2: Process Automation (Weeks 9-16)

**Week 9-10: Advanced AI Features**
- [ ] Implement intent recognition and classification
- [ ] Add multi-turn conversation support
- [ ] Create personalized recommendations
- [ ] Implement escalation to human agents
- [ ] Add conversation context management

**Week 11-12: Government Integration**
- [ ] Integrate with government identity systems
- [ ] Implement document validation APIs
- [ ] Add secure document upload and storage
- [ ] Create process tracking system
- [ ] Implement notification system

**Week 13-14: Admin Panel**
- [ ] Build admin dashboard for content management
- [ ] Create analytics and reporting features
- [ ] Implement user management system
- [ ] Add system monitoring and alerts
- [ ] Create knowledge base management tools

**Week 15-16: Production Readiness**
- [ ] Complete security audit and penetration testing
- [ ] Implement comprehensive monitoring and logging
- [ ] Set up automated backup and disaster recovery
- [ ] Conduct load testing and performance optimization
- [ ] Complete documentation and training materials

### Success Metrics and KPIs

**Technical Metrics:**
- Page load time < 2 seconds (LCP)
- AI response time < 3 seconds
- Search response time < 500ms
- System uptime > 99.9%
- Test coverage > 80%

**Business Metrics:**
- 70% reduction in consultation response times
- 85% citizen satisfaction score
- 60% automation of frequent procedures
- 50% reduction in call center volume
- 90% successful query resolution rate

**User Experience Metrics:**
- Task completion rate > 85%
- User retention rate > 70%
- Average session duration > 5 minutes
- Bounce rate < 30%
- Accessibility compliance score 100%

### Risk Mitigation

**Technical Risks:**
- **OpenAI API limitations:** Implement response caching and fallback mechanisms
- **Database performance:** Use proper indexing and connection pooling
- **Security vulnerabilities:** Regular security audits and dependency updates
- **Scalability issues:** Implement horizontal scaling and load balancing

**Business Risks:**
- **User adoption:** Comprehensive user testing and feedback integration
- **Government compliance:** Regular compliance reviews and legal consultation
- **Data privacy:** Implement privacy by design and GDPR compliance
- **Change management:** Stakeholder engagement and training programs

---

## Document Status

**Status:** Complete - Ready for Implementation
**Last Updated:** December 19, 2024
**Next Review:** January 19, 2025
**Approved By:** [Pending stakeholder review]

This comprehensive fullstack architecture document provides all necessary technical specifications to begin implementation of the Chia-Next AI-First Citizen Services Platform. The architecture balances government requirements for security and reliability with modern development practices for rapid iteration and scalability.

The implementation roadmap provides a clear path from initial setup to production deployment, with specific milestones and success metrics to ensure project success. All technical decisions are documented with rationale to support future architectural evolution and team onboarding.
```
