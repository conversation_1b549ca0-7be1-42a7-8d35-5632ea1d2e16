{"version": 2, "buildCommand": "cd apps/web && npm run build", "outputDirectory": "apps/web/.next", "installCommand": "npm install", "framework": "nextjs", "functions": {"apps/web/app/api/**/*.ts": {"runtime": "nodejs18.x"}}, "rewrites": [{"source": "/(.*)", "destination": "/apps/web/$1"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}], "env": {"NEXT_PUBLIC_SUPABASE_URL": "@supabase_url", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase_anon_key", "SUPABASE_SERVICE_ROLE_KEY": "@supabase_service_role_key", "NEXTAUTH_SECRET": "@nextauth_secret", "NEXTAUTH_URL": "@nextauth_url"}}