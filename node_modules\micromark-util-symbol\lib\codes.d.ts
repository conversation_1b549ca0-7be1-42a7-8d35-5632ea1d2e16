export namespace codes {
    let carriageReturn: -5;
    let lineFeed: -4;
    let carriageReturnLineFeed: -3;
    let horizontalTab: -2;
    let virtualSpace: -1;
    let eof: null;
    let nul: 0;
    let soh: 1;
    let stx: 2;
    let etx: 3;
    let eot: 4;
    let enq: 5;
    let ack: 6;
    let bel: 7;
    let bs: 8;
    let ht: 9;
    let lf: 10;
    let vt: 11;
    let ff: 12;
    let cr: 13;
    let so: 14;
    let si: 15;
    let dle: 16;
    let dc1: 17;
    let dc2: 18;
    let dc3: 19;
    let dc4: 20;
    let nak: 21;
    let syn: 22;
    let etb: 23;
    let can: 24;
    let em: 25;
    let sub: 26;
    let esc: 27;
    let fs: 28;
    let gs: 29;
    let rs: 30;
    let us: 31;
    let space: 32;
    let exclamationMark: 33;
    let quotationMark: 34;
    let numberSign: 35;
    let dollarSign: 36;
    let percentSign: 37;
    let ampersand: 38;
    let apostrophe: 39;
    let leftParenthesis: 40;
    let rightParenthesis: 41;
    let asterisk: 42;
    let plusSign: 43;
    let comma: 44;
    let dash: 45;
    let dot: 46;
    let slash: 47;
    let digit0: 48;
    let digit1: 49;
    let digit2: 50;
    let digit3: 51;
    let digit4: 52;
    let digit5: 53;
    let digit6: 54;
    let digit7: 55;
    let digit8: 56;
    let digit9: 57;
    let colon: 58;
    let semicolon: 59;
    let lessThan: 60;
    let equalsTo: 61;
    let greaterThan: 62;
    let questionMark: 63;
    let atSign: 64;
    let uppercaseA: 65;
    let uppercaseB: 66;
    let uppercaseC: 67;
    let uppercaseD: 68;
    let uppercaseE: 69;
    let uppercaseF: 70;
    let uppercaseG: 71;
    let uppercaseH: 72;
    let uppercaseI: 73;
    let uppercaseJ: 74;
    let uppercaseK: 75;
    let uppercaseL: 76;
    let uppercaseM: 77;
    let uppercaseN: 78;
    let uppercaseO: 79;
    let uppercaseP: 80;
    let uppercaseQ: 81;
    let uppercaseR: 82;
    let uppercaseS: 83;
    let uppercaseT: 84;
    let uppercaseU: 85;
    let uppercaseV: 86;
    let uppercaseW: 87;
    let uppercaseX: 88;
    let uppercaseY: 89;
    let uppercaseZ: 90;
    let leftSquareBracket: 91;
    let backslash: 92;
    let rightSquareBracket: 93;
    let caret: 94;
    let underscore: 95;
    let graveAccent: 96;
    let lowercaseA: 97;
    let lowercaseB: 98;
    let lowercaseC: 99;
    let lowercaseD: 100;
    let lowercaseE: 101;
    let lowercaseF: 102;
    let lowercaseG: 103;
    let lowercaseH: 104;
    let lowercaseI: 105;
    let lowercaseJ: 106;
    let lowercaseK: 107;
    let lowercaseL: 108;
    let lowercaseM: 109;
    let lowercaseN: 110;
    let lowercaseO: 111;
    let lowercaseP: 112;
    let lowercaseQ: 113;
    let lowercaseR: 114;
    let lowercaseS: 115;
    let lowercaseT: 116;
    let lowercaseU: 117;
    let lowercaseV: 118;
    let lowercaseW: 119;
    let lowercaseX: 120;
    let lowercaseY: 121;
    let lowercaseZ: 122;
    let leftCurlyBrace: 123;
    let verticalBar: 124;
    let rightCurlyBrace: 125;
    let tilde: 126;
    let del: 127;
    let byteOrderMarker: 65279;
    let replacementCharacter: 65533;
}
//# sourceMappingURL=codes.d.ts.map