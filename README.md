# CHIA - Portal Ciudadano Digital

Portal digital para servicios ciudadanos con IA integrada, desarrollado con Next.js 15 y Supabase.

## 🚀 Tecnologías

- **Frontend**: Next.js 15 con App Router y TypeScript 5.3+
- **Backend**: Supabase (PostgreSQL + Auth + Storage + Edge Functions)
- **Styling**: Tailwind CSS 3.4+ con configuración personalizada
- **Testing**: Jest + React Testing Library + Playwright
- **Deployment**: Vercel con CI/CD automatizado
- **Monorepo**: Turbo + npm workspaces

## 📋 Requisitos Previos

- Node.js 18+ 
- npm 9+
- Cuenta de Supabase
- Cuenta de Vercel (para deployment)

## 🛠️ Instalación y Configuración

### 1. Clonar el repositorio
```bash
git clone <repository-url>
cd chia-next
```

### 2. Instalar dependencias
```bash
npm install
```

### 3. Configurar variables de entorno
```bash
cp .env.example .env.local
```

Edita `.env.local` con tus credenciales:
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Security
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000
```

### 4. Configurar base de datos
```bash
# Inicializar Supabase localmente (opcional)
npx supabase init
npx supabase start

# O aplicar migraciones a tu proyecto Supabase
npx supabase db push
```

### 5. Ejecutar en desarrollo
```bash
npm run dev
```

## 📁 Estructura del Proyecto

```
chia-next/
├── apps/
│   └── web/                    # Aplicación Next.js principal
│       ├── app/               # App Router (Next.js 13+)
│       │   ├── (auth)/        # Grupo de rutas de autenticación
│       │   ├── (dashboard)/   # Grupo de rutas del dashboard
│       │   └── api/           # API Routes
│       ├── components/        # Componentes React
│       ├── lib/              # Utilidades y configuraciones
│       ├── types/            # Definiciones de tipos TypeScript
│       └── hooks/            # Custom React hooks
├── packages/
│   ├── ui/                   # Componentes UI compartidos
│   ├── database/             # Esquemas y utilidades de BD
│   ├── ai/                   # Servicios de IA
│   └── auth/                 # Utilidades de autenticación
├── supabase/
│   ├── migrations/           # Migraciones de base de datos
│   └── config.toml          # Configuración de Supabase
├── tests/                    # Pruebas unitarias e integración
├── docs/                     # Documentación del proyecto
│   └── stories/             # Historias de desarrollo
└── .github/
    └── workflows/           # GitHub Actions CI/CD
```

## 🧪 Testing

### Ejecutar pruebas unitarias
```bash
npm run test
```

### Ejecutar pruebas con cobertura
```bash
npm run test:coverage
```

### Ejecutar pruebas E2E
```bash
npm run test:e2e
```

## 🚀 Deployment

### Vercel (Recomendado)
1. Conecta tu repositorio a Vercel
2. Configura las variables de entorno en Vercel Dashboard
3. El deployment se ejecuta automáticamente con cada push a `main`

### Variables de entorno requeridas en Vercel:
- `NEXT_PUBLIC_SUPABASE_URL`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`
- `SUPABASE_SERVICE_ROLE_KEY`
- `NEXTAUTH_SECRET`
- `NEXTAUTH_URL`

## 🔧 Scripts Disponibles

```bash
npm run dev          # Ejecutar en desarrollo
npm run build        # Construir para producción
npm run start        # Ejecutar build de producción
npm run lint         # Ejecutar ESLint
npm run type-check   # Verificar tipos TypeScript
npm run test         # Ejecutar pruebas
npm run test:watch   # Ejecutar pruebas en modo watch
npm run test:coverage # Ejecutar pruebas con cobertura
```

## 🔐 Seguridad

El proyecto incluye:
- Headers de seguridad configurados
- Content Security Policy (CSP)
- Validación de entrada
- Rate limiting
- Row Level Security (RLS) en Supabase
- Sanitización de datos

## 📚 Documentación Adicional

- [Arquitectura del Sistema](docs/ARQUITECTURA.md)
- [Guía de Desarrollo](docs/DEVELOPMENT.md)
- [Historias de Usuario](docs/stories/)
- [API Documentation](docs/API.md)

## 🤝 Contribución

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT - ver el archivo [LICENSE](LICENSE) para detalles.

## 🆘 Soporte

Para soporte y preguntas:
- Crear un issue en GitHub
- Contactar al equipo de desarrollo

## 🎯 Estado del Proyecto

✅ **Story 1.1: Project Foundation and Infrastructure Setup** - Completado
- Configuración inicial de Next.js 15 con TypeScript
- Integración completa con Supabase
- Pipeline de CI/CD con Vercel
- Configuración de testing con Jest
- Implementación de seguridad y headers

🔄 **Próximas historias**: Ver [docs/stories/](docs/stories/) para el roadmap completo.
