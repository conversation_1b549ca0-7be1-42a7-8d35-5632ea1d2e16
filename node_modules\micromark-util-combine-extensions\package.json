{"name": "micromark-util-combine-extensions", "version": "2.0.1", "description": "micromark utility to combine syntax or html extensions", "license": "MIT", "keywords": ["micromark", "util", "utility", "extension", "combine", "merge"], "repository": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-combine-extensions", "bugs": "https://github.com/micromark/micromark/issues", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "files": ["index.d.ts.map", "index.d.ts", "index.js"], "exports": "./index.js", "dependencies": {"micromark-util-chunked": "^2.0.0", "micromark-util-types": "^2.0.0"}, "xo": {"envs": ["shared-node-browser"], "prettier": true, "rules": {"guard-for-in": "off", "unicorn/prefer-code-point": "off"}}}