/**
 * @import {Options, State} from 'mdast-util-to-markdown'
 */
/**
 * @param {State} state
 * @returns {Exclude<Options['bulletOrdered'], null | undefined>}
 */
export function checkBulletOrdered(state: State): Exclude<Options["bulletOrdered"], null | undefined>;
import type { State } from 'mdast-util-to-markdown';
import type { Options } from 'mdast-util-to-markdown';
//# sourceMappingURL=check-bullet-ordered.d.ts.map