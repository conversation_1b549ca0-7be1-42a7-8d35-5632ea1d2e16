import { createServerSupabase } from '@/lib/supabase';
import { getOptionalServerSession, getCurrentUserProfile } from '@/lib/auth';

// Mock the redirect function
jest.mock('next/navigation', () => ({
  redirect: jest.fn(),
}));

// Mock the supabase module
jest.mock('@/lib/supabase');

const mockCreateServerSupabase = createServerSupabase as jest.MockedFunction<
  typeof createServerSupabase
>;

describe('Auth utilities', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getOptionalServerSession', () => {
    it('returns session when user is authenticated', async () => {
      const mockSession = {
        user: { id: 'user-123', email: '<EMAIL>' },
        access_token: 'token-123',
      };

      const mockSupabase = {
        auth: {
          getSession: jest.fn().mockResolvedValue({
            data: { session: mockSession },
            error: null,
          }),
        },
      };

      mockCreateServerSupabase.mockReturnValue(mockSupabase as any);

      const result = await getOptionalServerSession();

      expect(result).toEqual(mockSession);
      expect(mockSupabase.auth.getSession).toHaveBeenCalledTimes(1);
    });

    it('returns null when no session exists', async () => {
      const mockSupabase = {
        auth: {
          getSession: jest.fn().mockResolvedValue({
            data: { session: null },
            error: null,
          }),
        },
      };

      mockCreateServerSupabase.mockReturnValue(mockSupabase as any);

      const result = await getOptionalServerSession();

      expect(result).toBeNull();
    });

    it('returns null when there is an error', async () => {
      const mockSupabase = {
        auth: {
          getSession: jest.fn().mockResolvedValue({
            data: { session: null },
            error: new Error('Auth error'),
          }),
        },
      };

      mockCreateServerSupabase.mockReturnValue(mockSupabase as any);

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      const result = await getOptionalServerSession();

      expect(result).toBeNull();
      expect(consoleSpy).toHaveBeenCalledWith(
        'Error getting session:',
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });

  describe('getCurrentUserProfile', () => {
    it('returns user profile when session exists', async () => {
      const mockSession = {
        user: { id: 'user-123', email: '<EMAIL>' },
        access_token: 'token-123',
      };

      const mockProfile = {
        id: 'profile-123',
        auth_id: 'user-123',
        nombre: 'Juan',
        apellido: 'Pérez',
        email: '<EMAIL>',
      };

      const mockSupabase = {
        auth: {
          getSession: jest.fn().mockResolvedValue({
            data: { session: mockSession },
            error: null,
          }),
        },
        from: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValue({
            data: mockProfile,
            error: null,
          }),
        }),
      };

      mockCreateServerSupabase.mockReturnValue(mockSupabase as any);

      const result = await getCurrentUserProfile();

      expect(result).toEqual(mockProfile);
      expect(mockSupabase.from).toHaveBeenCalledWith('ciudadanos');
    });

    it('returns null when profile fetch fails', async () => {
      const mockSession = {
        user: { id: 'user-123', email: '<EMAIL>' },
        access_token: 'token-123',
      };

      const mockSupabase = {
        auth: {
          getSession: jest.fn().mockResolvedValue({
            data: { session: mockSession },
            error: null,
          }),
        },
        from: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValue({
            data: null,
            error: new Error('Profile not found'),
          }),
        }),
      };

      mockCreateServerSupabase.mockReturnValue(mockSupabase as any);

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      const result = await getCurrentUserProfile();

      expect(result).toBeNull();
      expect(consoleSpy).toHaveBeenCalledWith(
        'Error fetching user profile:',
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });
});
