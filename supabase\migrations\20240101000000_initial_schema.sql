-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON>reate custom types
CREATE TYPE tipo_documento AS ENUM ('CC', 'CE', 'TI', 'PP');
CREATE TYPE tipo_servicio AS ENUM ('tramite', 'consulta', 'pago', 'certificado');

-- Create ciudadanos table
CREATE TABLE ciudadanos (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    auth_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    nombre VARCHAR(100) NOT NULL,
    apellido VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    telefono VARCHAR(20),
    documento_identidad VARCHAR(20) NOT NULL UNIQUE,
    tipo_documento tipo_documento NOT NULL,
    fecha_nacimiento DATE,
    direccion TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create servicios_ciudadanos table
CREATE TABLE servicios_ciudadanos (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    nombre VARCHAR(200) NOT NULL,
    descripcion_corta TEXT NOT NULL,
    descripcion_larga TEXT,
    tipo_servicio tipo_servicio NOT NULL,
    categoria VARCHAR(100) NOT NULL,
    tiempo_estimado_minutos INTEGER,
    costo DECIMAL(10,2),
    requisitos TEXT[],
    documentos_requeridos TEXT[],
    activo BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create solicitudes table
CREATE TABLE solicitudes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ciudadano_id UUID REFERENCES ciudadanos(id) ON DELETE CASCADE,
    servicio_id UUID REFERENCES servicios_ciudadanos(id) ON DELETE RESTRICT,
    numero_solicitud VARCHAR(50) UNIQUE NOT NULL,
    estado VARCHAR(50) DEFAULT 'pendiente',
    fecha_solicitud TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    fecha_respuesta TIMESTAMP WITH TIME ZONE,
    observaciones TEXT,
    documentos_adjuntos JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_ciudadanos_auth_id ON ciudadanos(auth_id);
CREATE INDEX idx_ciudadanos_documento ON ciudadanos(documento_identidad);
CREATE INDEX idx_servicios_categoria ON servicios_ciudadanos(categoria);
CREATE INDEX idx_servicios_tipo ON servicios_ciudadanos(tipo_servicio);
CREATE INDEX idx_solicitudes_ciudadano ON solicitudes(ciudadano_id);
CREATE INDEX idx_solicitudes_servicio ON solicitudes(servicio_id);
CREATE INDEX idx_solicitudes_estado ON solicitudes(estado);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_ciudadanos_updated_at BEFORE UPDATE ON ciudadanos FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_servicios_updated_at BEFORE UPDATE ON servicios_ciudadanos FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_solicitudes_updated_at BEFORE UPDATE ON solicitudes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE ciudadanos ENABLE ROW LEVEL SECURITY;
ALTER TABLE servicios_ciudadanos ENABLE ROW LEVEL SECURITY;
ALTER TABLE solicitudes ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for ciudadanos
CREATE POLICY "Users can view own profile" ON ciudadanos
    FOR SELECT USING (auth.uid() = auth_id);

CREATE POLICY "Users can update own profile" ON ciudadanos
    FOR UPDATE USING (auth.uid() = auth_id);

CREATE POLICY "Users can insert own profile" ON ciudadanos
    FOR INSERT WITH CHECK (auth.uid() = auth_id);

-- Create RLS policies for servicios_ciudadanos (public read)
CREATE POLICY "Anyone can view active services" ON servicios_ciudadanos
    FOR SELECT USING (activo = true);

-- Create RLS policies for solicitudes
CREATE POLICY "Users can view own requests" ON solicitudes
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM ciudadanos 
            WHERE ciudadanos.id = solicitudes.ciudadano_id 
            AND ciudadanos.auth_id = auth.uid()
        )
    );

CREATE POLICY "Users can create own requests" ON solicitudes
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM ciudadanos 
            WHERE ciudadanos.id = solicitudes.ciudadano_id 
            AND ciudadanos.auth_id = auth.uid()
        )
    );

-- Insert sample services
INSERT INTO servicios_ciudadanos (nombre, descripcion_corta, descripcion_larga, tipo_servicio, categoria, tiempo_estimado_minutos, costo, requisitos, documentos_requeridos) VALUES
('Certificado de Residencia', 'Certificado que acredita el lugar de residencia', 'Documento oficial que certifica el domicilio actual del ciudadano para trámites legales y administrativos.', 'certificado', 'Certificados', 15, 5000.00, ARRAY['Ser mayor de edad', 'Residir en el municipio'], ARRAY['Cédula de ciudadanía', 'Recibo de servicios públicos']),
('Paz y Salvo Predial', 'Certificado de estar al día con impuesto predial', 'Documento que certifica que el contribuyente se encuentra al día con sus obligaciones del impuesto predial.', 'certificado', 'Impuestos', 20, 8000.00, ARRAY['Ser propietario del predio', 'Estar al día con pagos'], ARRAY['Cédula de ciudadanía', 'Escritura del predio']),
('Licencia de Construcción', 'Permiso para construcción de obras', 'Autorización municipal para adelantar obras de construcción, ampliación, modificación y demolición.', 'tramite', 'Urbanismo', 1440, 150000.00, ARRAY['Ser propietario del lote', 'Cumplir normas urbanísticas'], ARRAY['Planos arquitectónicos', 'Estudio de suelos', 'Escritura del lote']);
