# **CHIA - Plan de Testing UX**
## *Estrategia Completa de Validación y Testing de Experiencia de Usuario*

---

## **1. Estrategia General de Testing**

### **Pirámide de Testing UX**

```mermaid
graph TD
    A[Testing Manual Exploratorio] --> B[Testing de Usabilidad]
    B --> C[Testing de Accesibilidad]
    C --> D[Testing Automatizado]
    D --> E[Monitoreo Continuo]
    
    style A fill:#ff6b6b
    style B fill:#ffa726
    style C fill:#66bb6a
    style D fill:#42a5f5
    style E fill:#ab47bc
```

### **Fases de Testing**

#### **Fase 1: Testing de Desarrollo (Continuo)**
- **Frecuencia**: Con cada commit
- **Scope**: Componentes individuales
- **Herramientas**: Jest, React Testing Library, Storybook
- **Responsable**: Desarrolladores

#### **Fase 2: Testing de Integración (Semanal)**
- **Frecuencia**: Cada sprint
- **Scope**: Flujos completos
- **Herramientas**: Cypress, Playwright
- **Responsable**: QA Team

#### **Fase 3: Testing de Usuario (Mensual)**
- **Frecuencia**: Cada release mayor
- **Scope**: Experiencia completa
- **Herramientas**: UserTesting, Maze, Hotjar
- **Responsable**: UX Team

#### **Fase 4: Testing de Accesibilidad (Continuo)**
- **Frecuencia**: Con cada deploy
- **Scope**: Compliance WCAG 2.2
- **Herramientas**: axe-core, Pa11y, WAVE
- **Responsable**: Todo el equipo

---

## **2. Testing Automatizado**

### **Unit Testing - Componentes**

```javascript
// Ejemplo: Testing de componente Button
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from '../Button';

describe('Button Component', () => {
  test('renders with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button')).toHaveTextContent('Click me');
  });

  test('handles click events', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  test('is accessible', () => {
    render(<Button disabled>Disabled button</Button>);
    const button = screen.getByRole('button');
    
    expect(button).toHaveAttribute('aria-disabled', 'true');
    expect(button).toBeDisabled();
  });

  test('has proper focus management', () => {
    render(<Button>Focusable button</Button>);
    const button = screen.getByRole('button');
    
    button.focus();
    expect(button).toHaveFocus();
  });
});
```

### **Integration Testing - Flujos**

```javascript
// Ejemplo: Testing de flujo de autenticación
describe('Authentication Flow', () => {
  test('complete login process', async () => {
    // Arrange
    render(<App />);
    
    // Act - Navigate to login
    fireEvent.click(screen.getByText('Ingresar'));
    
    // Assert - Login form is visible
    expect(screen.getByLabelText('Número de cédula')).toBeInTheDocument();
    
    // Act - Fill form
    fireEvent.change(screen.getByLabelText('Número de cédula'), {
      target: { value: '12********' }
    });
    fireEvent.change(screen.getByLabelText('Contraseña'), {
      target: { value: 'password123' }
    });
    
    // Act - Submit
    fireEvent.click(screen.getByRole('button', { name: 'Iniciar Sesión' }));
    
    // Assert - Redirect to dashboard
    await waitFor(() => {
      expect(screen.getByText('Mi Carpeta Ciudadana')).toBeInTheDocument();
    });
  });

  test('handles authentication errors', async () => {
    // Mock failed authentication
    server.use(
      rest.post('/api/auth/login', (req, res, ctx) => {
        return res(ctx.status(401), ctx.json({ error: 'Invalid credentials' }));
      })
    );

    render(<App />);
    
    // Attempt login with invalid credentials
    fireEvent.click(screen.getByText('Ingresar'));
    fireEvent.change(screen.getByLabelText('Número de cédula'), {
      target: { value: 'invalid' }
    });
    fireEvent.click(screen.getByRole('button', { name: 'Iniciar Sesión' }));
    
    // Assert error message is shown
    await waitFor(() => {
      expect(screen.getByText('Credenciales inválidas')).toBeInTheDocument();
    });
  });
});
```

### **E2E Testing - Cypress**

```javascript
// cypress/integration/tramite-cedula.spec.js
describe('Renovación de Cédula - E2E', () => {
  beforeEach(() => {
    cy.login('12********', 'password123');
  });

  it('completes cedula renewal process', () => {
    // Navigate to cedula renewal
    cy.visit('/mi-carpeta');
    cy.contains('Renovar Cédula').click();
    
    // Fill personal information
    cy.get('[data-testid="nombres"]').type('Juan Carlos');
    cy.get('[data-testid="apellidos"]').type('Pérez González');
    cy.get('[data-testid="cedula-actual"]').type('12********');
    
    // Upload documents
    cy.get('[data-testid="upload-cedula"]').attachFile('cedula-frente.jpg');
    cy.get('[data-testid="upload-cedula-reverso"]').attachFile('cedula-reverso.jpg');
    
    // Verify document processing
    cy.contains('Documentos procesados correctamente', { timeout: 10000 });
    
    // Continue to payment
    cy.contains('Continuar al Pago').click();
    
    // Complete payment
    cy.get('[data-testid="payment-method"]').select('PSE');
    cy.get('[data-testid="bank"]').select('Bancolombia');
    cy.contains('Procesar Pago').click();
    
    // Verify success
    cy.contains('Trámite radicado exitosamente', { timeout: 15000 });
    cy.get('[data-testid="radicado-number"]').should('be.visible');
  });

  it('handles document upload errors', () => {
    cy.visit('/tramites/cedula');
    
    // Try to upload invalid file
    cy.get('[data-testid="upload-cedula"]').attachFile('invalid-file.txt');
    
    // Verify error message
    cy.contains('Formato de archivo no válido');
    cy.contains('Solo se permiten imágenes JPG, PNG o PDF');
  });

  it('is accessible via keyboard navigation', () => {
    cy.visit('/tramites/cedula');
    
    // Navigate using only keyboard
    cy.get('body').tab();
    cy.focused().should('contain', 'Nombres completos');
    
    cy.focused().type('Juan Carlos').tab();
    cy.focused().should('contain', 'Apellidos completos');
    
    // Verify skip links work
    cy.get('body').type('{alt}s');
    cy.focused().should('contain', 'Ir al contenido principal');
  });
});
```

---

## **3. Testing de Accesibilidad**

### **Automated Accessibility Testing**

```javascript
// jest-axe configuration
import { axe, toHaveNoViolations } from 'jest-axe';

expect.extend(toHaveNoViolations);

describe('Accessibility Tests', () => {
  test('homepage has no accessibility violations', async () => {
    const { container } = render(<Homepage />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('form components are accessible', async () => {
    const { container } = render(
      <FormField 
        label="Número de cédula" 
        type="text" 
        required 
        error="Campo requerido"
      />
    );
    
    const results = await axe(container);
    expect(results).toHaveNoViolations();
    
    // Additional manual checks
    const input = screen.getByLabelText('Número de cédula');
    expect(input).toHaveAttribute('aria-required', 'true');
    expect(input).toHaveAttribute('aria-invalid', 'true');
    expect(input).toHaveAttribute('aria-describedby');
  });
});
```

### **Manual Accessibility Checklist**

#### **Keyboard Navigation**
- [ ] Todos los elementos interactivos son accesibles por teclado
- [ ] El orden de tabulación es lógico
- [ ] Los indicadores de foco son visibles
- [ ] No hay trampas de teclado
- [ ] Los skip links funcionan correctamente

#### **Screen Reader Compatibility**
- [ ] Todos los elementos tienen etiquetas apropiadas
- [ ] Los landmarks están correctamente definidos
- [ ] Los cambios dinámicos se anuncian (aria-live)
- [ ] Las imágenes tienen texto alternativo
- [ ] Los formularios tienen instrucciones claras

#### **Visual Accessibility**
- [ ] Contraste de color cumple WCAG 2.2 AA
- [ ] El texto es legible al 200% de zoom
- [ ] No se usa solo color para transmitir información
- [ ] Las animaciones respetan prefers-reduced-motion

---

## **4. Testing de Usabilidad**

### **Metodología de Testing**

#### **Participantes**
- **Perfil 1**: Ciudadanos 18-35 años, alta familiaridad tecnológica
- **Perfil 2**: Ciudadanos 36-55 años, familiaridad tecnológica media
- **Perfil 3**: Ciudadanos 55+ años, baja familiaridad tecnológica
- **Perfil 4**: Usuarios con discapacidades (visual, motora, cognitiva)

#### **Tareas de Testing**

##### **Tarea 1: Consulta Básica con IA**
- **Objetivo**: Realizar una consulta sobre requisitos para renovar cédula
- **Criterio de éxito**: Obtener respuesta completa en < 2 minutos
- **Métricas**: Tiempo de tarea, tasa de éxito, satisfacción

##### **Tarea 2: Proceso de Autenticación**
- **Objetivo**: Ingresar al sistema usando cédula y contraseña
- **Criterio de éxito**: Acceso exitoso en < 1 minuto
- **Métricas**: Intentos fallidos, tiempo de tarea, errores

##### **Tarea 3: Renovación de Cédula Completa**
- **Objetivo**: Completar todo el proceso de renovación
- **Criterio de éxito**: Obtener número de radicado en < 15 minutos
- **Métricas**: Tasa de abandono, errores por paso, satisfacción

##### **Tarea 4: Recuperación de Errores**
- **Objetivo**: Recuperarse de un error de validación de documentos
- **Criterio de éxito**: Corregir error y continuar proceso
- **Métricas**: Comprensión del error, tiempo de recuperación

### **Protocolo de Testing**

#### **Preparación**
1. **Setup técnico**: Grabación de pantalla y audio
2. **Briefing**: Explicar objetivos sin revelar detalles específicos
3. **Consentimiento**: Autorización para grabación y uso de datos
4. **Ambiente**: Dispositivo del usuario o ambiente controlado

#### **Ejecución**
1. **Think-aloud protocol**: Usuario verbaliza pensamientos
2. **Observación no intrusiva**: Facilitador toma notas
3. **Intervención mínima**: Solo si el usuario está completamente bloqueado
4. **Captura de métricas**: Tiempo, clicks, errores

#### **Post-Testing**
1. **Entrevista semi-estructurada**: Percepción general
2. **System Usability Scale (SUS)**: Puntuación cuantitativa
3. **Net Promoter Score**: Probabilidad de recomendación
4. **Feedback específico**: Mejoras sugeridas

### **Métricas de Usabilidad**

#### **Métricas Cuantitativas**
- **Task Success Rate**: > 90% para tareas críticas
- **Time on Task**: Dentro del rango esperado por tarea
- **Error Rate**: < 5% para flujos principales
- **System Usability Scale**: > 80 puntos

#### **Métricas Cualitativas**
- **Satisfaction Rating**: > 4/5 en escala Likert
- **Perceived Ease of Use**: > 4/5 en escala Likert
- **Trust Score**: > 4/5 para manejo de datos personales
- **Likelihood to Recommend**: > 7/10 (NPS)

---

## **5. Testing de Performance UX**

### **Core Web Vitals Monitoring**

```javascript
// Performance monitoring setup
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

function sendToAnalytics(metric) {
  // Send to your analytics service
  gtag('event', metric.name, {
    event_category: 'Web Vitals',
    value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
    event_label: metric.id,
    non_interaction: true,
  });
}

// Measure all Core Web Vitals
getCLS(sendToAnalytics);
getFID(sendToAnalytics);
getFCP(sendToAnalytics);
getLCP(sendToAnalytics);
getTTFB(sendToAnalytics);
```

### **User-Centric Performance Testing**

#### **Synthetic Testing**
- **Herramienta**: Lighthouse CI
- **Frecuencia**: Con cada deploy
- **Dispositivos**: Mobile, Desktop
- **Conexiones**: 3G, 4G, WiFi

#### **Real User Monitoring**
- **Herramienta**: Google Analytics 4 + Custom metrics
- **Métricas**: Core Web Vitals + métricas custom
- **Segmentación**: Por dispositivo, ubicación, tipo de usuario

### **Performance Budget**

```json
{
  "budgets": [
    {
      "path": "/*",
      "timings": [
        { "metric": "first-contentful-paint", "budget": 2000 },
        { "metric": "largest-contentful-paint", "budget": 2500 },
        { "metric": "first-input-delay", "budget": 100 }
      ],
      "resourceSizes": [
        { "resourceType": "script", "budget": 250 },
        { "resourceType": "total", "budget": 1000 }
      ]
    }
  ]
}
```

---

## **6. Continuous Testing Strategy**

### **CI/CD Integration**

```yaml
# GitHub Actions workflow
name: UX Testing Pipeline

on: [push, pull_request]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Run unit tests
        run: npm test -- --coverage
      - name: Upload coverage
        uses: codecov/codecov-action@v1

  accessibility-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Run accessibility tests
        run: npm run test:a11y
      - name: Pa11y CI
        run: npx pa11y-ci --sitemap http://localhost:3000/sitemap.xml

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Run E2E tests
        run: npm run test:e2e
      - name: Upload test results
        uses: actions/upload-artifact@v2
        if: failure()
        with:
          name: cypress-screenshots
          path: cypress/screenshots

  performance-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Lighthouse CI
        run: |
          npm install -g @lhci/cli@0.8.x
          lhci autorun
```

### **Quality Gates**

#### **Pre-merge Requirements**
- [ ] All unit tests pass (100%)
- [ ] Accessibility tests pass (0 violations)
- [ ] Performance budget met
- [ ] Code coverage > 80%

#### **Pre-release Requirements**
- [ ] E2E tests pass (100%)
- [ ] Usability testing completed
- [ ] Performance regression tests pass
- [ ] Security audit completed

---

## **7. Reporting y Métricas**

### **Dashboard de Métricas UX**

#### **Métricas en Tiempo Real**
- **Core Web Vitals**: LCP, FID, CLS
- **User Satisfaction**: Ratings y feedback
- **Task Completion**: Por flujo de usuario
- **Error Rates**: Por componente y flujo

#### **Reportes Semanales**
- **Usability Metrics**: SUS scores, task success rates
- **Accessibility Compliance**: WCAG violations, remediation status
- **Performance Trends**: Core Web Vitals evolution
- **User Feedback**: Qualitative insights y action items

#### **Reportes Mensuales**
- **UX Health Score**: Métrica compuesta de usabilidad
- **Competitive Analysis**: Benchmarking con otras entidades
- **ROI de UX**: Impacto en conversión y satisfacción
- **Roadmap de Mejoras**: Priorización basada en datos

---

*Este plan de testing garantiza una experiencia de usuario de alta calidad, accesible y performante, con validación continua y mejora iterativa basada en datos reales de usuarios.*
