/**
 * Aadd support for parsing from markdown.
 *
 * @param {Readonly<Options> | null | undefined} [options]
 *   Configuration (optional).
 * @returns {undefined}
 *   Nothing.
 */
export default function remarkParse(options?: Readonly<Options> | null | undefined): undefined;
export default class remarkParse {
    /**
     * Aadd support for parsing from markdown.
     *
     * @param {Readonly<Options> | null | undefined} [options]
     *   Configuration (optional).
     * @returns {undefined}
     *   Nothing.
     */
    constructor(options?: Readonly<Options> | null | undefined);
    parser: (document: string, file: import("vfile").VFile) => import("mdast").Root;
}
export type Root = import('mdast').Root;
export type FromMarkdownOptions = import('mdast-util-from-markdown').Options;
export type Parser = import('unified').Parser<Root>;
export type Processor = import('unified').Processor<Root>;
export type Options = Omit<FromMarkdownOptions, 'extensions' | 'mdastExtensions'>;
