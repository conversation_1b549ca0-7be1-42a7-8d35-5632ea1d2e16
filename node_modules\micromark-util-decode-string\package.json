{"name": "micromark-util-decode-string", "version": "2.0.1", "description": "micromark utility to decode markdown strings", "license": "MIT", "keywords": ["micromark", "util", "utility", "decode", "character", "reference", "escape", "string"], "repository": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-decode-string", "bugs": "https://github.com/micromark/micromark/issues", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "files": ["dev/", "index.d.ts.map", "index.d.ts", "index.js"], "exports": {"development": "./dev/index.js", "default": "./index.js"}, "dependencies": {"micromark-util-character": "^2.0.0", "micromark-util-decode-numeric-character-reference": "^2.0.0", "micromark-util-symbol": "^2.0.0", "decode-named-character-reference": "^1.0.0"}, "scripts": {"build": "micromark-build"}, "xo": {"envs": ["shared-node-browser"], "prettier": true, "rules": {"unicorn/prefer-code-point": "off", "unicorn/prefer-string-replace-all": "off"}}}