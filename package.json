{"name": "chia-next", "version": "1.0.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "test": "turbo run test", "lint": "turbo run lint", "type-check": "turbo run type-check", "clean": "turbo run clean", "setup": "./scripts/setup.sh"}, "devDependencies": {"@types/node": "^20.0.0", "eslint": "^8.0.0", "eslint-config-next": "^15.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "jest": "^29.0.0", "jest-environment-jsdom": "^29.0.0", "@testing-library/react": "^14.0.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/user-event": "^14.0.0", "turbo": "^1.10.0", "typescript": "^5.3.0"}, "eslintConfig": {"extends": ["next/core-web-vitals", "@typescript-eslint/recommended"]}, "dependencies": {"@kayvan/markdown-tree-parser": "^1.6.0"}}