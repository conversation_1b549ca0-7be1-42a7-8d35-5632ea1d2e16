import { render, screen } from '@testing-library/react';
import HomePage from '@/app/page';

// Mock the Next.js Image component
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props: any) => {
    // eslint-disable-next-line @next/next/no-img-element
    return <img {...props} />;
  },
}));

describe('HomePage', () => {
  it('renders the main heading', () => {
    render(<HomePage />);
    
    const heading = screen.getByRole('heading', {
      name: /portal ciudadano digital/i,
    });
    
    expect(heading).toBeInTheDocument();
  });

  it('renders the three main feature cards', () => {
    render(<HomePage />);
    
    // Check for the three main features
    expect(screen.getByText('Trámites en Línea')).toBeInTheDocument();
    expect(screen.getByText('Asistente IA')).toBeInTheDocument();
    expect(screen.getByText('Seguimiento')).toBeInTheDocument();
  });

  it('renders the feature descriptions', () => {
    render(<HomePage />);
    
    expect(
      screen.getByText(/realiza tus trámites de manera rápida y segura/i)
    ).toBeInTheDocument();
    expect(
      screen.getByText(/obtén ayuda personalizada con nuestro asistente/i)
    ).toBeInTheDocument();
    expect(
      screen.getByText(/consulta el estado de tus solicitudes/i)
    ).toBeInTheDocument();
  });

  it('has proper accessibility structure', () => {
    render(<HomePage />);
    
    // Check for proper heading hierarchy
    const mainHeading = screen.getByRole('heading', { level: 1 });
    expect(mainHeading).toBeInTheDocument();
    
    // Check for feature headings
    const featureHeadings = screen.getAllByRole('heading', { level: 3 });
    expect(featureHeadings).toHaveLength(3);
  });

  it('renders action buttons for each feature', () => {
    render(<HomePage />);
    
    const buttons = screen.getAllByRole('button');
    expect(buttons).toHaveLength(3);
    
    // Check button text
    expect(screen.getByRole('button', { name: /iniciar/i })).toBeInTheDocument();
  });
});
